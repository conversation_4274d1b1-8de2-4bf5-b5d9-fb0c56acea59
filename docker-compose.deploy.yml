version: '3.8'

services:
  # Combined Server + Client in single container
  c-aibox-combined:
    image: ${COMBINED_IMAGE_NAME:-c-aibox-combined}:${IMAGE_TAG:-latest}
    container_name: ${COMBINED_CONTAINER_NAME:-c-aibox-combined}
    restart: unless-stopped
    ports:
      - "${SERVER_HOST_PORT:-8080}:${SERVER_CONTAINER_PORT:-8080}"
      - "${CLIENT_VNC_PORT:-5900}:5900"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    environment:
      - DISPLAY=${X11_DISPLAY:-:0}
      - VNC_RESOLUTION=${VNC_GEOMETRY:-1920x1080}
      - VNC_DEPTH=${VNC_DEPTH:-24}
      - VNC_PASSWORD=${VNC_PASSWORD:-c-aibox123}
      - SERVER_HOST=${SERVER_HOST:-0.0.0.0}
      - SERVER_PORT=${SERVER_CONTAINER_PORT:-8080}
      - DISPLAY_MODE=${DISPLAY_MODE:-gui}
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s
    profiles:
      - combined

  # Separate Server container
  c-aibox-server:
    image: ${SERVER_IMAGE_NAME:-c-aibox-mock-server}:${IMAGE_TAG:-latest}
    container_name: ${SERVER_CONTAINER_NAME:-c-aibox-server}
    restart: unless-stopped
    ports:
      - "${SERVER_HOST_PORT:-8080}:${SERVER_CONTAINER_PORT:-8080}"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
    environment:
      - SERVER_HOST=${SERVER_HOST:-0.0.0.0}
      - SERVER_PORT=${SERVER_CONTAINER_PORT:-8080}
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    profiles:
      - separate

  # Separate Client GUI container (Qt5)
  c-aibox-client:
    image: ${CLIENT_IMAGE_NAME:-c-aibox-client}:${IMAGE_TAG:-latest}
    container_name: ${CLIENT_CONTAINER_NAME:-c-aibox-client}
    restart: unless-stopped
    ports:
      - "${CLIENT_VNC_PORT:-5900}:5900"
      - "${CLIENT_WEB_NGINX_PORT:-8081}:8081"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    environment:
      - DISPLAY=${X11_DISPLAY:-:0}
      - VNC_RESOLUTION=${VNC_GEOMETRY:-1920x1080}
      - VNC_DEPTH=${VNC_DEPTH:-24}
      - VNC_PASSWORD=${VNC_PASSWORD:-c-aibox123}
      - DISPLAY_MODE=${DISPLAY_MODE:-gui}
      - CLIENT_TYPE=${CLIENT_TYPE:-qt5}
    env_file:
      - .env
    depends_on:
      - c-aibox-server
    healthcheck:
      test: ["CMD", "pgrep", "-f", "client_app"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s
    profiles:
      - separate

  # Separate Web Client container
  c-aibox-web-client:
    image: ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest}
    container_name: ${CLIENT_WEB_CONTAINER_NAME:-c-aibox-web-client}
    restart: unless-stopped
    ports:
      - "${CLIENT_WEB_PORT:-3000}:3000"
      - "${CLIENT_WEB_NGINX_PORT:-8081}:80"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - WEB_CLIENT_PORT=${WEB_CLIENT_PORT:-3000}
      - WEB_CLIENT_HOST=${WEB_CLIENT_HOST:-0.0.0.0}
      - WEB_CLIENT_DEV_MODE=${WEB_CLIENT_DEV_MODE:-true}
    env_file:
      - .env
    depends_on:
      - c-aibox-server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s
    profiles:
      - web-client
      - separate-web

volumes:
  data:
    driver: local
  logs:
    driver: local
  models:
    driver: local

networks:
  default:
    name: c-aibox-network
