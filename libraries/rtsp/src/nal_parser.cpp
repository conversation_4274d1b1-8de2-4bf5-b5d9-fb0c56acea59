#include "rtsp/nal_parser.hpp"
#include <sched.h>
#include <unistd.h>
#include <iostream>
#include <algorithm>

namespace aibox {
namespace rtsp {

// NALParser implementation
NALParser::NALParser(const NALParsingConfig& config)
    : config_(config)
    , hardware_status_(HardwareAccelStatus::DISABLED)
    , should_stop_(false) {
    
    // Optimize for RK3588
    optimizeForRK3588();
    
    // Initialize hardware acceleration if enabled
    if (config_.enable_mpp_parsing) {
        initializeHardwareAcceleration();
    }
    
    // Validate configuration
    if (!validateConfig()) {
        throw std::invalid_argument("Invalid NAL parsing configuration");
    }
    
    std::cout << "[NALParser] Initialized with hardware acceleration: " 
              << (hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE ? "enabled" : "disabled") << std::endl;
}

NALParser::~NALParser() {
    should_stop_ = true;
    
    // Wait for worker threads
    for (auto& thread : worker_threads_) {
        if (thread && thread->joinable()) {
            thread->join();
        }
    }
    
    cleanupHardwareAcceleration();
    std::cout << "[NALParser] Destroyed" << std::endl;
}

void NALParser::updateConfig(const NALParsingConfig& new_config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    config_ = new_config;
    
    // Reinitialize hardware if needed
    if (new_config.enable_mpp_parsing && hardware_status_ == HardwareAccelStatus::DISABLED) {
        initializeHardwareAcceleration();
    } else if (!new_config.enable_mpp_parsing && hardware_status_ != HardwareAccelStatus::DISABLED) {
        cleanupHardwareAcceleration();
    }
}

const NALParsingConfig& NALParser::getConfig() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return config_;
}

std::vector<NALUnit> NALParser::parseRTPPayload(const std::vector<uint8_t>& payload, 
                                               const StreamId& stream_id,
                                               Timestamp timestamp) {
    std::vector<NALUnit> nal_units;
    
    if (payload.empty()) {
        return nal_units;
    }
    
    // Check for NAL unit start codes (0x00 0x00 0x00 0x01 or 0x00 0x00 0x01)
    size_t pos = 0;
    while (pos < payload.size()) {
        // Find next start code
        size_t start_code_pos = findNextStartCode(payload, pos);
        if (start_code_pos == std::string::npos) {
            break;
        }
        
        // Find end of NAL unit
        size_t next_start_code = findNextStartCode(payload, start_code_pos + 4);
        size_t nal_end = (next_start_code != std::string::npos) ? next_start_code : payload.size();
        
        // Extract NAL unit data
        std::vector<uint8_t> nal_data(payload.begin() + start_code_pos + 4, payload.begin() + nal_end);
        
        if (!nal_data.empty()) {
            NALUnit nal_unit;
            if (parseNALUnit(nal_data, nal_unit)) {
                nal_unit.stream_id = stream_id;
                nal_unit.timestamp = timestamp;
                nal_units.push_back(nal_unit);
                
                // Update statistics
                updateParsingStatistics(nal_unit, hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE);
                
                // Invoke callback
                std::lock_guard<std::mutex> lock(callback_mutex_);
                if (nal_callback_) {
                    nal_callback_(nal_unit);
                }
            }
        }
        
        pos = nal_end;
    }
    
    return nal_units;
}

bool NALParser::parseNALUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    if (data.empty()) {
        return false;
    }
    
    // Validate NAL header if enabled
    if (config_.validate_nal_headers && !validateNALHeader(data)) {
        statistics_.invalid_nal_units++;
        return false;
    }
    
    // Detect NAL unit type
    nal_unit.type = detectNALUnitType(data);
    if (nal_unit.type == NALUnitType::UNKNOWN) {
        statistics_.invalid_nal_units++;
        return false;
    }
    
    // Copy data
    nal_unit.data = data;
    
    // Detect if this is a keyframe
    nal_unit.is_keyframe = isKeyframe(nal_unit);
    
    // Process with hardware if available
    bool hardware_processed = false;
    if (hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE && config_.enable_mpp_parsing) {
        hardware_processed = processWithMPP(data, nal_unit);
    }
    
    if (!hardware_processed) {
        // Fallback to software processing
        hardware_processed = processWithSoftware(data, nal_unit);
    }
    
    statistics_.nal_units_parsed++;
    return hardware_processed;
}

NALUnitType NALParser::detectNALUnitType(const std::vector<uint8_t>& data) {
    if (data.empty()) {
        return NALUnitType::UNKNOWN;
    }
    
    uint8_t nal_header = data[0];
    
    // Check if this is H.264 or H.265
    if ((nal_header & 0x80) == 0) {
        // H.264 NAL unit
        uint8_t nal_type = nal_header & 0x1F;
        switch (nal_type) {
            case 1: return NALUnitType::H264_NON_IDR;
            case 5: return NALUnitType::H264_IDR;
            case 7: return NALUnitType::H264_SPS;
            case 8: return NALUnitType::H264_PPS;
            default: return NALUnitType::UNKNOWN;
        }
    } else {
        // H.265 NAL unit
        uint8_t nal_type = (nal_header >> 1) & 0x3F;
        switch (nal_type) {
            case 19: return NALUnitType::H265_IDR_W_RADL;
            case 20: return NALUnitType::H265_IDR_N_LP;
            case 32: return NALUnitType::H265_VPS;
            case 33: return NALUnitType::H265_SPS;
            case 34: return NALUnitType::H265_PPS;
            default: return NALUnitType::UNKNOWN;
        }
    }
}

bool NALParser::isKeyframe(const NALUnit& nal_unit) {
    switch (nal_unit.type) {
        case NALUnitType::H264_IDR:
        case NALUnitType::H265_IDR_W_RADL:
        case NALUnitType::H265_IDR_N_LP:
            return true;
        default:
            return false;
    }
}

bool NALParser::extractStreamInfo(const std::vector<NALUnit>& nal_units, VideoStreamInfo& info) {
    bool info_updated = false;
    
    for (const auto& nal_unit : nal_units) {
        if (updateStreamInfo(nal_unit, info)) {
            info_updated = true;
        }
    }
    
    return info_updated;
}

bool NALParser::updateStreamInfo(const NALUnit& nal_unit, VideoStreamInfo& info) {
    bool updated = false;
    
    switch (nal_unit.type) {
        case NALUnitType::H264_SPS:
            if (parseSPS(nal_unit.data, info)) {
                info.codec = VideoCodec::H264;
                info.has_sps = true;
                info.sps_data = nal_unit.data;
                updated = true;
                statistics_.sps_units_found++;
            }
            break;
            
        case NALUnitType::H264_PPS:
            if (parsePPS(nal_unit.data, info)) {
                info.has_pps = true;
                info.pps_data = nal_unit.data;
                updated = true;
                statistics_.pps_units_found++;
            }
            break;
            
        case NALUnitType::H265_VPS:
            if (parseVPS(nal_unit.data, info)) {
                info.codec = VideoCodec::H265;
                info.has_vps = true;
                info.vps_data = nal_unit.data;
                updated = true;
            }
            break;
            
        case NALUnitType::H265_SPS:
            if (parseSPS(nal_unit.data, info)) {
                info.codec = VideoCodec::H265;
                info.has_sps = true;
                info.sps_data = nal_unit.data;
                updated = true;
                statistics_.sps_units_found++;
            }
            break;
            
        case NALUnitType::H265_PPS:
            if (parsePPS(nal_unit.data, info)) {
                info.has_pps = true;
                info.pps_data = nal_unit.data;
                updated = true;
                statistics_.pps_units_found++;
            }
            break;
            
        case NALUnitType::H264_IDR:
        case NALUnitType::H265_IDR_W_RADL:
        case NALUnitType::H265_IDR_N_LP:
            statistics_.idr_frames_found++;
            break;
            
        default:
            break;
    }
    
    return updated;
}

VideoStreamInfo NALParser::getCurrentStreamInfo(const StreamId& stream_id) const {
    std::lock_guard<std::mutex> lock(stream_info_mutex_);
    
    auto it = stream_info_map_.find(stream_id);
    if (it != stream_info_map_.end()) {
        return it->second;
    }
    
    return VideoStreamInfo{};
}

bool NALParser::enableHardwareAcceleration(bool enable) {
    if (enable && hardware_status_ == HardwareAccelStatus::DISABLED) {
        initializeHardwareAcceleration();
    } else if (!enable && hardware_status_ != HardwareAccelStatus::DISABLED) {
        cleanupHardwareAcceleration();
    }
    
    return hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE;
}

bool NALParser::isHardwareAccelerationEnabled() const {
    return hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE;
}

HardwareAccelStatus NALParser::getHardwareStatus() const {
    return hardware_status_;
}

void NALParser::setNALUnitCallback(std::function<void(const NALUnit&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    nal_callback_ = callback;
}

void NALParser::setStreamInfoCallback(std::function<void(const StreamId&, const VideoStreamInfo&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    stream_info_callback_ = callback;
}

void NALParser::setErrorCallback(StreamErrorCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    error_callback_ = callback;
}

NALParsingStatistics NALParser::getStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    // Create a copy manually since atomic types can't be copied
    NALParsingStatistics copy;
    copy.nal_units_parsed = statistics_.nal_units_parsed.load();
    copy.sps_units_found = statistics_.sps_units_found.load();
    copy.pps_units_found = statistics_.pps_units_found.load();
    copy.idr_frames_found = statistics_.idr_frames_found.load();
    copy.p_frames_found = statistics_.p_frames_found.load();
    copy.b_frames_found = statistics_.b_frames_found.load();
    copy.parsing_errors = statistics_.parsing_errors.load();
    copy.invalid_nal_units = statistics_.invalid_nal_units.load();
    copy.hardware_accelerated_count = statistics_.hardware_accelerated_count.load();
    copy.software_fallback_count = statistics_.software_fallback_count.load();

    return copy;
}

size_t NALParser::getMemoryUsage() const {
    size_t total = sizeof(*this);
    
    // Stream info map memory
    {
        std::lock_guard<std::mutex> lock(stream_info_mutex_);
        total += stream_info_map_.size() * (sizeof(StreamId) + sizeof(VideoStreamInfo));
    }
    
    // Hardware processor memory
    if (hardware_processor_) {
        total += hardware_processor_->getMemoryUsage();
    }
    
    return total;
}

uint32_t NALParser::getActiveStreamCount() const {
    std::lock_guard<std::mutex> lock(stream_info_mutex_);
    return static_cast<uint32_t>(stream_info_map_.size());
}

void NALParser::handleThermalThrottling(int temperature) {
    if (temperature >= 80) {
        // Disable hardware acceleration to reduce heat
        if (hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE) {
            hardware_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
            std::cout << "[NALParser] Thermal throttling: switching to software parsing" << std::endl;
        }
    } else if (temperature < 75) {
        // Re-enable hardware acceleration
        if (hardware_status_ == HardwareAccelStatus::SOFTWARE_FALLBACK && config_.enable_mpp_parsing) {
            hardware_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
            std::cout << "[NALParser] Thermal throttling: re-enabling hardware parsing" << std::endl;
        }
    }
}

void NALParser::setPerformanceMode(bool high_performance) {
    if (high_performance && config_.enable_mpp_parsing) {
        if (hardware_status_ == HardwareAccelStatus::DISABLED) {
            initializeHardwareAcceleration();
        }
    } else {
        if (hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE) {
            hardware_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
        }
    }
}

// Private methods
void NALParser::initializeHardwareAcceleration() {
    try {
        hardware_processor_ = std::make_unique<HardwareNALProcessor>(config_);
        if (hardware_processor_->initialize()) {
            hardware_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
            std::cout << "[NALParser] Hardware acceleration initialized successfully" << std::endl;
        } else {
            hardware_status_ = HardwareAccelStatus::HARDWARE_ERROR;
            std::cout << "[NALParser] Hardware acceleration initialization failed" << std::endl;
        }
    } catch (const std::exception& e) {
        hardware_status_ = HardwareAccelStatus::HARDWARE_ERROR;
        std::cout << "[NALParser] Hardware acceleration error: " << e.what() << std::endl;
    }
}

void NALParser::cleanupHardwareAcceleration() {
    if (hardware_processor_) {
        hardware_processor_->cleanup();
        hardware_processor_.reset();
    }
    hardware_status_ = HardwareAccelStatus::DISABLED;
}

void NALParser::optimizeForRK3588() {
    setCPUAffinity();
    
    // Start worker threads if parallel parsing is enabled
    if (config_.enable_parallel_parsing && config_.worker_thread_count > 0) {
        worker_threads_.reserve(config_.worker_thread_count);
        for (int i = 0; i < config_.worker_thread_count; ++i) {
            // TODO: Implement worker threads for parallel parsing
        }
    }
}

void NALParser::setCPUAffinity() {
    // Set CPU affinity to RK3588 efficiency cores
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(2, &cpuset);  // Cortex-A55 core
    CPU_SET(3, &cpuset);  // Cortex-A55 core
    
    if (sched_setaffinity(0, sizeof(cpuset), &cpuset) != 0) {
        std::cout << "[NALParser] Warning: Failed to set CPU affinity" << std::endl;
    }
}

size_t NALParser::findNextStartCode(const std::vector<uint8_t>& data, size_t start_pos) {
    for (size_t i = start_pos; i < data.size() - 3; ++i) {
        if (data[i] == 0x00 && data[i+1] == 0x00) {
            if (data[i+2] == 0x00 && data[i+3] == 0x01) {
                return i;  // Found 4-byte start code
            } else if (data[i+2] == 0x01) {
                return i+1;  // Found 3-byte start code
            }
        }
    }
    return std::string::npos;
}

bool NALParser::parseH264NALUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    // TODO: Implement H.264 specific parsing
    return processWithSoftware(data, nal_unit);
}

bool NALParser::parseH265NALUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    // TODO: Implement H.265 specific parsing
    return processWithSoftware(data, nal_unit);
}

bool NALParser::validateNALHeader(const std::vector<uint8_t>& data) {
    if (data.empty()) return false;

    // Basic NAL header validation
    uint8_t header = data[0];

    // Check forbidden bit (should be 0)
    if (header & 0x80) return false;

    return true;
}

bool NALParser::parseSPS(const std::vector<uint8_t>& sps_data, VideoStreamInfo& info) {
    // TODO: Implement SPS parsing to extract resolution, framerate, etc.
    // This is a simplified implementation
    if (sps_data.size() < 4) return false;

    // For now, set default values
    info.resolution = Resolution{1920, 1080};
    info.framerate = FrameRate{30, 1};

    return true;
}

bool NALParser::parsePPS(const std::vector<uint8_t>& pps_data, VideoStreamInfo& info) {
    // TODO: Implement PPS parsing
    return !pps_data.empty();
}

bool NALParser::parseVPS(const std::vector<uint8_t>& vps_data, VideoStreamInfo& info) {
    // TODO: Implement VPS parsing for H.265
    return !vps_data.empty();
}

bool NALParser::processWithMPP(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    if (!hardware_processor_ || hardware_status_ != HardwareAccelStatus::HARDWARE_ACTIVE) {
        return false;
    }

    return hardware_processor_->processNALUnit(data, nal_unit);
}

bool NALParser::processWithSoftware(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    // Software fallback processing
    nal_unit.data = data;
    nal_unit.type = detectNALUnitType(data);
    nal_unit.is_keyframe = isKeyframe(nal_unit);

    statistics_.software_fallback_count++;
    return true;
}

void NALParser::handleParsingError(ErrorCategory category, const std::string& message) {
    statistics_.parsing_errors++;

    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (error_callback_) {
        error_callback_("", category, message);
    }
}

void NALParser::handleHardwareError(const std::string& component, const std::string& error) {
    std::cout << "[NALParser] Hardware error in " << component << ": " << error << std::endl;

    if (component == "MPP") {
        hardware_status_ = HardwareAccelStatus::HARDWARE_ERROR;
    }
}

void NALParser::updateParsingStatistics(const NALUnit& nal_unit, bool hardware_accelerated) {
    if (hardware_accelerated) {
        statistics_.hardware_accelerated_count++;
    } else {
        statistics_.software_fallback_count++;
    }

    // Update frame type statistics
    switch (nal_unit.type) {
        case NALUnitType::H264_IDR:
        case NALUnitType::H265_IDR_W_RADL:
        case NALUnitType::H265_IDR_N_LP:
            statistics_.idr_frames_found++;
            break;
        default:
            break;
    }
}

void NALParser::updateStreamStatistics(const StreamId& stream_id, const VideoStreamInfo& info) {
    std::lock_guard<std::mutex> lock(stream_info_mutex_);
    stream_info_map_[stream_id] = info;

    // Invoke stream info callback
    std::lock_guard<std::mutex> cb_lock(callback_mutex_);
    if (stream_info_callback_) {
        stream_info_callback_(stream_id, info);
    }
}

bool NALParser::validateConfig() const {
    return config_.max_nal_size_bytes > 0 &&
           config_.parsing_timeout_ms > 0 &&
           config_.worker_thread_count >= 0 &&
           config_.max_consecutive_errors > 0;
}

bool NALParser::checkHardwareCapabilities() const {
    // TODO: Implement hardware capability checking
    return true;
}

// HardwareNALProcessor implementation
HardwareNALProcessor::HardwareNALProcessor(const NALParsingConfig& config)
    : config_(config)
    , initialized_(false)
    , status_(HardwareAccelStatus::DISABLED)
    , processed_count_(0)
    , mpp_context_(nullptr)
    , rga_context_(nullptr) {
}

HardwareNALProcessor::~HardwareNALProcessor() {
    cleanup();
}

bool HardwareNALProcessor::initialize() {
    if (initialized_) return true;

    // Initialize MPP decoder
    if (!initializeMPP()) {
        status_ = HardwareAccelStatus::HARDWARE_ERROR;
        return false;
    }

    // Initialize RGA if needed
    if (config_.enable_zero_copy && !initializeRGA()) {
        std::cout << "[HardwareNALProcessor] RGA initialization failed, continuing without zero-copy" << std::endl;
    }

    initialized_ = true;
    status_ = HardwareAccelStatus::HARDWARE_ACTIVE;

    std::cout << "[HardwareNALProcessor] Hardware acceleration initialized" << std::endl;
    return true;
}

void HardwareNALProcessor::cleanup() {
    if (!initialized_) return;

    cleanupMPP();
    cleanupRGA();

    initialized_ = false;
    status_ = HardwareAccelStatus::DISABLED;

    std::cout << "[HardwareNALProcessor] Hardware acceleration cleaned up" << std::endl;
}

bool HardwareNALProcessor::isInitialized() const {
    return initialized_;
}

bool HardwareNALProcessor::processNALUnit(const std::vector<uint8_t>& input, NALUnit& output) {
    if (!initialized_ || status_ != HardwareAccelStatus::HARDWARE_ACTIVE) {
        return false;
    }

    // TODO: Implement actual MPP processing
    // For now, just copy the data
    output.data = input;
    processed_count_++;

    return true;
}

bool HardwareNALProcessor::validateNALUnit(const std::vector<uint8_t>& data) {
    // TODO: Implement hardware-accelerated validation
    return !data.empty();
}

HardwareAccelStatus HardwareNALProcessor::getStatus() const {
    return status_;
}

size_t HardwareNALProcessor::getMemoryUsage() const {
    // TODO: Calculate actual memory usage
    return sizeof(*this) + 1024 * 1024;  // Estimate 1MB
}

uint32_t HardwareNALProcessor::getProcessedCount() const {
    return processed_count_;
}

bool HardwareNALProcessor::initializeMPP() {
    // TODO: Implement actual MPP initialization
    mpp_context_ = nullptr;  // Placeholder
    return false;  // Not implemented yet
}

void HardwareNALProcessor::cleanupMPP() {
    // TODO: Implement MPP cleanup
    mpp_context_ = nullptr;
}

bool HardwareNALProcessor::initializeRGA() {
    // TODO: Implement RGA initialization
    rga_context_ = nullptr;  // Placeholder
    return false;  // Not implemented yet
}

void HardwareNALProcessor::cleanupRGA() {
    // TODO: Implement RGA cleanup
    rga_context_ = nullptr;
}

} // namespace rtsp
} // namespace aibox
