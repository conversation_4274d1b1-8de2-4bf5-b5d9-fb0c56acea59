#include "rtsp/rtsp_config.hpp"
#include <fstream>
#include <sstream>
#include <sys/sysinfo.h>
#include <unistd.h>

// Conditional JSON support
#ifdef RTSP_HAS_JSON
#include <nlohmann/json.hpp>
using json = nlohmann::json;
#endif

namespace aibox {
namespace rtsp {

// RTSPModuleConfig implementation
void RTSPModuleConfig::autoConfigurePlatform() {
    if (!auto_detect_platform && !platform_override.empty()) {
        // Use manual override
        if (platform_override == "4gb") {
            performance.configureFor4GB();
        } else if (platform_override == "8gb") {
            performance.configureFor8GB();
        }
        return;
    }
    
    // Auto-detect based on available memory
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        // Convert to MB
        unsigned long total_memory_mb = info.totalram / (1024 * 1024);

        if (total_memory_mb >= 14000) {  // 16GB system (accounting for kernel usage)
            performance.configureFor16GB();
        } else if (total_memory_mb >= 7000) {  // 8GB system (accounting for kernel usage)
            performance.configureFor8GB();
        } else {
            performance.configureFor4GB();
        }
    } else {
        // Fallback to conservative 4GB configuration
        performance.configureFor4GB();
    }
}

// RTSPConfigManager implementation
Result<RTSPModuleConfig> RTSPConfigManager::loadFromFile(const std::string& file_path) {
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            return Result<RTSPModuleConfig>("Failed to open config file: " + file_path, 
                                          ErrorCategory::CONFIGURATION_ERROR);
        }
        
        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        file.close();
        
        return loadFromJson(content);
    } catch (const std::exception& e) {
        return Result<RTSPModuleConfig>("Failed to read config file: " + std::string(e.what()),
                                      ErrorCategory::CONFIGURATION_ERROR);
    }
}

Result<bool> RTSPConfigManager::saveToFile(const RTSPModuleConfig& config, const std::string& file_path) {
    try {
        auto json_result = toJson(config);
        if (!json_result) {
            return Result<bool>(json_result.error_message, json_result.error_category);
        }
        
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return Result<bool>("Failed to create config file: " + file_path,
                              ErrorCategory::CONFIGURATION_ERROR);
        }
        
        file << *json_result;
        file.close();
        
        return Result<bool>(true);
    } catch (const std::exception& e) {
        return Result<bool>("Failed to write config file: " + std::string(e.what()),
                          ErrorCategory::CONFIGURATION_ERROR);
    }
}

Result<RTSPModuleConfig> RTSPConfigManager::loadFromJson(const std::string& json_string) {
#ifdef RTSP_HAS_JSON
    try {
        json j = json::parse(json_string);
        RTSPModuleConfig config;
        
        // Parse main module settings
        if (j.contains("rtsp_module")) {
            auto& module = j["rtsp_module"];
            
            if (module.contains("version")) config.version = module["version"];
            if (module.contains("enabled")) config.enabled = module["enabled"];
            if (module.contains("auto_detect_platform")) config.auto_detect_platform = module["auto_detect_platform"];
            if (module.contains("platform_override")) config.platform_override = module["platform_override"];
            
            // Parse performance settings
            if (module.contains("performance")) {
                auto& perf = module["performance"];
                if (perf.contains("max_concurrent_streams")) config.performance.max_concurrent_streams = perf["max_concurrent_streams"];
                if (perf.contains("thread_pool_size")) config.performance.thread_pool_size = perf["thread_pool_size"];
                if (perf.contains("max_memory_usage_mb")) config.performance.max_memory_usage_mb = perf["max_memory_usage_mb"];
                if (perf.contains("cpu_usage_limit_percent")) config.performance.cpu_usage_limit_percent = perf["cpu_usage_limit_percent"];
                if (perf.contains("enable_mpp_decoder")) config.performance.enable_mpp_decoder = perf["enable_mpp_decoder"];
                if (perf.contains("enable_rga_scaler")) config.performance.enable_rga_scaler = perf["enable_rga_scaler"];
                if (perf.contains("thermal_management")) config.performance.thermal_management = perf["thermal_management"];
                
                if (perf.contains("cpu_affinity") && perf["cpu_affinity"].is_array()) {
                    config.performance.cpu_affinity.clear();
                    for (auto& core : perf["cpu_affinity"]) {
                        config.performance.cpu_affinity.push_back(core);
                    }
                }
            }
            
            // Parse network settings
            if (module.contains("network")) {
                auto& net = module["network"];
                if (net.contains("prefer_tcp")) config.network.prefer_tcp = net["prefer_tcp"];
                if (net.contains("tcp_nodelay")) config.network.tcp_nodelay = net["tcp_nodelay"];
                if (net.contains("udp_buffer_size")) config.network.udp_buffer_size = net["udp_buffer_size"];
                if (net.contains("keep_alive_interval_ms")) config.network.keep_alive_interval_ms = net["keep_alive_interval_ms"];
                if (net.contains("ssl_verify_peer")) config.network.ssl_verify_peer = net["ssl_verify_peer"];
            }
            
            // Parse monitoring settings
            if (module.contains("monitoring")) {
                auto& mon = module["monitoring"];
                if (mon.contains("enable_statistics")) config.monitoring.enable_statistics = mon["enable_statistics"];
                if (mon.contains("statistics_interval_ms")) config.monitoring.statistics_interval_ms = mon["statistics_interval_ms"];
                if (mon.contains("log_level")) config.monitoring.log_level = mon["log_level"];
                if (mon.contains("log_file_path")) config.monitoring.log_file_path = mon["log_file_path"];
                if (mon.contains("enable_performance_monitoring")) config.monitoring.enable_performance_monitoring = mon["enable_performance_monitoring"];
            }
        }
        
        // Parse streams
        if (j.contains("streams") && j["streams"].is_array()) {
            config.streams.clear();
            for (auto& stream_json : j["streams"]) {
                RTSPConnectionConfig stream;
                
                if (stream_json.contains("rtsp_url")) stream.rtsp_url = stream_json["rtsp_url"];
                if (stream_json.contains("username")) stream.username = stream_json["username"];
                if (stream_json.contains("password")) stream.password = stream_json["password"];
                if (stream_json.contains("enabled")) stream.enabled = stream_json["enabled"];
                if (stream_json.contains("timeout_ms")) stream.timeout_ms = stream_json["timeout_ms"];
                if (stream_json.contains("retry_count")) stream.retry_count = stream_json["retry_count"];
                if (stream_json.contains("buffer_size_bytes")) stream.buffer_size_bytes = stream_json["buffer_size_bytes"];
                if (stream_json.contains("queue_size")) stream.queue_size = stream_json["queue_size"];
                if (stream_json.contains("use_mpp_decoder")) stream.use_mpp_decoder = stream_json["use_mpp_decoder"];
                if (stream_json.contains("use_rga_scaler")) stream.use_rga_scaler = stream_json["use_rga_scaler"];
                
                // Parse transport protocol
                if (stream_json.contains("transport")) {
                    std::string transport = stream_json["transport"];
                    if (transport == "tcp") stream.transport = TransportProtocol::TCP;
                    else if (transport == "udp") stream.transport = TransportProtocol::UDP;
                    else stream.transport = TransportProtocol::AUTO;
                }
                
                // Parse priority
                if (stream_json.contains("priority")) {
                    std::string priority = stream_json["priority"];
                    if (priority == "low") stream.priority = StreamPriority::LOW;
                    else if (priority == "medium") stream.priority = StreamPriority::MEDIUM;
                    else if (priority == "high") stream.priority = StreamPriority::HIGH;
                    else if (priority == "critical") stream.priority = StreamPriority::CRITICAL;
                }
                
                // Parse metadata
                if (stream_json.contains("metadata") && stream_json["metadata"].is_object()) {
                    for (auto& [key, value] : stream_json["metadata"].items()) {
                        if (value.is_string()) {
                            stream.metadata[key] = value;
                        }
                    }
                }
                
                config.streams.push_back(stream);
            }
        }
        
        // Auto-configure platform if needed
        config.autoConfigurePlatform();
        
        // Validate configuration
        auto validation_result = validate(config);
        if (!validation_result) {
            return Result<RTSPModuleConfig>(validation_result.error_message, validation_result.error_category);
        }
        
        return Result<RTSPModuleConfig>(config);
        
    } catch (const json::parse_error& e) {
        return Result<RTSPModuleConfig>("JSON parse error: " + std::string(e.what()),
                                      ErrorCategory::CONFIGURATION_ERROR);
    } catch (const std::exception& e) {
        return Result<RTSPModuleConfig>("Configuration error: " + std::string(e.what()),
                                      ErrorCategory::CONFIGURATION_ERROR);
    }
#else
    return Result<RTSPModuleConfig>("JSON support not available", ErrorCategory::CONFIGURATION_ERROR);
#endif
}

Result<std::string> RTSPConfigManager::toJson(const RTSPModuleConfig& config) {
#ifdef RTSP_HAS_JSON
    try {
        json j;
        
        // Main module settings
        j["rtsp_module"]["version"] = config.version;
        j["rtsp_module"]["enabled"] = config.enabled;
        j["rtsp_module"]["auto_detect_platform"] = config.auto_detect_platform;
        j["rtsp_module"]["platform_override"] = config.platform_override;
        
        // Performance settings
        auto& perf = j["rtsp_module"]["performance"];
        perf["max_concurrent_streams"] = config.performance.max_concurrent_streams;
        perf["thread_pool_size"] = config.performance.thread_pool_size;
        perf["max_memory_usage_mb"] = config.performance.max_memory_usage_mb;
        perf["cpu_usage_limit_percent"] = config.performance.cpu_usage_limit_percent;
        perf["enable_mpp_decoder"] = config.performance.enable_mpp_decoder;
        perf["enable_rga_scaler"] = config.performance.enable_rga_scaler;
        perf["thermal_management"] = config.performance.thermal_management;
        perf["cpu_affinity"] = config.performance.cpu_affinity;
        
        // Network settings
        auto& net = j["rtsp_module"]["network"];
        net["prefer_tcp"] = config.network.prefer_tcp;
        net["tcp_nodelay"] = config.network.tcp_nodelay;
        net["udp_buffer_size"] = config.network.udp_buffer_size;
        net["keep_alive_interval_ms"] = config.network.keep_alive_interval_ms;
        net["ssl_verify_peer"] = config.network.ssl_verify_peer;
        
        // Monitoring settings
        auto& mon = j["rtsp_module"]["monitoring"];
        mon["enable_statistics"] = config.monitoring.enable_statistics;
        mon["statistics_interval_ms"] = config.monitoring.statistics_interval_ms;
        mon["log_level"] = config.monitoring.log_level;
        mon["log_file_path"] = config.monitoring.log_file_path;
        mon["enable_performance_monitoring"] = config.monitoring.enable_performance_monitoring;
        
        // Streams
        j["streams"] = json::array();
        for (const auto& stream : config.streams) {
            json stream_json;
            stream_json["rtsp_url"] = stream.rtsp_url;
            stream_json["username"] = stream.username;
            stream_json["password"] = stream.password;
            stream_json["enabled"] = stream.enabled;
            stream_json["timeout_ms"] = stream.timeout_ms;
            stream_json["retry_count"] = stream.retry_count;
            stream_json["buffer_size_bytes"] = stream.buffer_size_bytes;
            stream_json["queue_size"] = stream.queue_size;
            stream_json["use_mpp_decoder"] = stream.use_mpp_decoder;
            stream_json["use_rga_scaler"] = stream.use_rga_scaler;
            
            // Transport protocol
            switch (stream.transport) {
                case TransportProtocol::TCP: stream_json["transport"] = "tcp"; break;
                case TransportProtocol::UDP: stream_json["transport"] = "udp"; break;
                case TransportProtocol::AUTO: stream_json["transport"] = "auto"; break;
            }
            
            // Priority
            switch (stream.priority) {
                case StreamPriority::LOW: stream_json["priority"] = "low"; break;
                case StreamPriority::MEDIUM: stream_json["priority"] = "medium"; break;
                case StreamPriority::HIGH: stream_json["priority"] = "high"; break;
                case StreamPriority::CRITICAL: stream_json["priority"] = "critical"; break;
            }
            
            // Metadata
            if (!stream.metadata.empty()) {
                stream_json["metadata"] = stream.metadata;
            }
            
            j["streams"].push_back(stream_json);
        }
        
        return Result<std::string>(j.dump(2));  // Pretty print with 2 spaces
        
    } catch (const std::exception& e) {
        return Result<std::string>("JSON serialization error: " + std::string(e.what()),
                                  ErrorCategory::CONFIGURATION_ERROR);
    }
#else
    return Result<std::string>("JSON support not available", ErrorCategory::CONFIGURATION_ERROR);
#endif
}

Result<bool> RTSPConfigManager::validate(const RTSPModuleConfig& config) {
    // Basic validation
    if (!config.isValid()) {
        return Result<bool>("Invalid configuration: resource limits exceeded",
                          ErrorCategory::CONFIGURATION_ERROR);
    }
    
    // Validate individual streams
    for (const auto& stream : config.streams) {
        if (!validateStreamConfig(stream)) {
            return Result<bool>("Invalid stream configuration: " + stream.rtsp_url,
                              ErrorCategory::CONFIGURATION_ERROR);
        }
    }
    
    // Validate performance configuration
    if (!validatePerformanceConfig(config.performance)) {
        return Result<bool>("Invalid performance configuration",
                          ErrorCategory::CONFIGURATION_ERROR);
    }
    
    return Result<bool>(true);
}

RTSPModuleConfig RTSPConfigManager::createDefault() {
    RTSPModuleConfig config;
    config.autoConfigurePlatform();
    return config;
}

RTSPModuleConfig RTSPConfigManager::createFor4GB() {
    RTSPModuleConfig config;
    config.performance.configureFor4GB();
    config.auto_detect_platform = false;
    config.platform_override = "4gb";
    return config;
}

RTSPModuleConfig RTSPConfigManager::createFor8GB() {
    RTSPModuleConfig config;
    config.performance.configureFor8GB();
    config.auto_detect_platform = false;
    config.platform_override = "8gb";
    return config;
}

RTSPModuleConfig RTSPConfigManager::createFor16GB() {
    RTSPModuleConfig config;
    config.performance.configureFor16GB();
    config.auto_detect_platform = false;
    config.platform_override = "16gb";
    return config;
}

// Private helper methods
bool RTSPConfigManager::validateStreamConfig(const RTSPConnectionConfig& stream) {
    return stream.isValid() &&
           stream.timeout_ms > 0 &&
           stream.retry_count >= 0 &&
           stream.buffer_size_bytes > 0 &&
           stream.queue_size > 0;
}

bool RTSPConfigManager::validatePerformanceConfig(const RTSPPerformanceConfig& perf) {
    return perf.max_concurrent_streams > 0 &&
           perf.max_memory_usage_mb > 0 &&
           perf.thread_pool_size > 0 &&
           perf.cpu_usage_limit_percent > 0.0f &&
           perf.cpu_usage_limit_percent <= 100.0f;
}

} // namespace rtsp
} // namespace aibox
