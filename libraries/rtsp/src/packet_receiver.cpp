#include "rtsp/packet_receiver.hpp"
#include <sched.h>
#include <unistd.h>
#include <sys/syscall.h>
#include <iostream>

namespace aibox {
namespace rtsp {

// PacketReceiver implementation
PacketReceiver::PacketReceiver(const RTSPConnectionConfig& config)
    : config_(config)
    , transport_mode_(config.transport)
    , should_stop_(false)
    , is_receiving_(false)
    , rga_status_(HardwareAccelStatus::DISABLED)
    , dmabuf_enabled_(false)
    , hardware_processing_(false)
    , adaptive_quality_(false)
    , packet_loss_threshold_(0.05f)
    , max_latency_ms_(200)
    , thermal_throttling_(false) {
    
    // Initialize jitter buffer configuration
    jitter_config_.initial_size_ms = config_.jitter_buffer_size_ms;
    jitter_config_.target_latency_ms = 100;
    jitter_config_.adaptive_sizing = true;
    
    // Optimize for RK3588
    optimizeForRK3588();
    
    // Validate configuration
    if (!validateConfig()) {
        throw std::invalid_argument("Invalid packet receiver configuration");
    }
    
    std::cout << "[PacketReceiver] Initialized for stream: " << config_.rtsp_url << std::endl;
}

PacketReceiver::~PacketReceiver() {
    stopReceiving();
    cleanupRGAScaler();
    cleanupDMABuf();
    std::cout << "[PacketReceiver] Destroyed" << std::endl;
}

bool PacketReceiver::startReceiving() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (is_receiving_) {
        return true;
    }
    
    std::cout << "[PacketReceiver] Starting packet reception" << std::endl;
    
    // Initialize hardware acceleration if enabled
    if (config_.use_rga_scaler && !initializeRGAScaler()) {
        std::cout << "[PacketReceiver] RGA scaler initialization failed, using software fallback" << std::endl;
    }
    
    if (config_.use_dmabuf_zerocopy && !initializeDMABuf()) {
        std::cout << "[PacketReceiver] DMABUF initialization failed, using regular memory" << std::endl;
    }
    
    // Create jitter buffer
    jitter_buffer_ = std::make_unique<HardwareJitterBuffer>(jitter_config_);
    
    // Start worker threads
    should_stop_ = false;
    receiver_thread_ = std::make_unique<std::thread>(&PacketReceiver::receiverWorker, this);
    processor_thread_ = std::make_unique<std::thread>(&PacketReceiver::processorWorker, this);
    
    is_receiving_ = true;
    
    std::cout << "[PacketReceiver] Packet reception started successfully" << std::endl;
    return true;
}

void PacketReceiver::stopReceiving() {
    if (!is_receiving_) {
        return;
    }
    
    std::cout << "[PacketReceiver] Stopping packet reception" << std::endl;
    
    should_stop_ = true;
    
    // Wait for threads to finish
    if (receiver_thread_ && receiver_thread_->joinable()) {
        receiver_thread_->join();
    }
    if (processor_thread_ && processor_thread_->joinable()) {
        processor_thread_->join();
    }
    
    // Cleanup components
    jitter_buffer_.reset();
    
    is_receiving_ = false;
    
    std::cout << "[PacketReceiver] Packet reception stopped" << std::endl;
}

bool PacketReceiver::isReceiving() const {
    return is_receiving_;
}

void PacketReceiver::updateConfig(const RTSPConnectionConfig& new_config) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    config_ = new_config;
    transport_mode_ = new_config.transport;
    
    // Update jitter buffer if needed
    if (jitter_buffer_) {
        jitter_config_.initial_size_ms = new_config.jitter_buffer_size_ms;
        jitter_buffer_->updateConfig(jitter_config_);
    }
    
    std::cout << "[PacketReceiver] Configuration updated" << std::endl;
}

void PacketReceiver::configureJitterBuffer(const JitterBufferConfig& config) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    jitter_config_ = config;
    
    if (jitter_buffer_) {
        jitter_buffer_->updateConfig(config);
    }
}

const JitterBufferConfig& PacketReceiver::getJitterBufferConfig() const {
    return jitter_config_;
}

void PacketReceiver::setTransportMode(TransportProtocol mode) {
    transport_mode_ = mode;
}

TransportProtocol PacketReceiver::getTransportMode() const {
    return transport_mode_;
}

bool PacketReceiver::enableRGAScaling(bool enable) {
    if (enable && !initializeRGAScaler()) {
        return false;
    } else if (!enable) {
        cleanupRGAScaler();
    }
    return true;
}

bool PacketReceiver::enableDMABufZeroCopy(bool enable) {
    if (enable && !initializeDMABuf()) {
        return false;
    } else if (!enable) {
        cleanupDMABuf();
    }
    dmabuf_enabled_ = enable;
    return true;
}

HardwareAccelStatus PacketReceiver::getRGAStatus() const {
    return rga_status_;
}

bool PacketReceiver::isDMABufEnabled() const {
    return dmabuf_enabled_;
}

void PacketReceiver::setPacketCallback(std::function<void(const std::vector<uint8_t>&, Timestamp)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    packet_callback_ = callback;
}

void PacketReceiver::setNALUnitCallback(std::function<void(const NALUnit&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    nal_callback_ = callback;
}

void PacketReceiver::setErrorCallback(StreamErrorCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    error_callback_ = callback;
}

PacketStatistics PacketReceiver::getStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    // Create a copy manually since atomic types can't be copied
    PacketStatistics copy;
    copy.packets_received = statistics_.packets_received.load();
    copy.packets_lost = statistics_.packets_lost.load();
    copy.bytes_received = statistics_.bytes_received.load();
    copy.duplicate_packets = statistics_.duplicate_packets.load();
    copy.out_of_order_packets = statistics_.out_of_order_packets.load();
    copy.jitter_buffer_overruns = statistics_.jitter_buffer_overruns.load();
    copy.jitter_buffer_underruns = statistics_.jitter_buffer_underruns.load();
    copy.average_jitter_ms = statistics_.average_jitter_ms.load();
    copy.current_queue_depth = statistics_.current_queue_depth.load();
    copy.hardware_accelerated_count = statistics_.hardware_accelerated_count.load();
    copy.software_fallback_count = statistics_.software_fallback_count.load();

    return copy;
}

float PacketReceiver::getCurrentJitterMs() const {
    if (jitter_buffer_) {
        return jitter_buffer_->getCurrentJitterMs();
    }
    return 0.0f;
}

uint32_t PacketReceiver::getQueueDepth() const {
    if (jitter_buffer_) {
        return jitter_buffer_->getQueueDepth();
    }
    return 0;
}

size_t PacketReceiver::getMemoryUsage() const {
    size_t total = 0;
    
    // Base memory usage
    total += sizeof(*this);
    
    // Jitter buffer memory
    if (jitter_buffer_) {
        total += jitter_buffer_->getMemoryUsage();
    }
    
    // Component memory (estimated)
    total += config_.buffer_size_bytes * 2;  // Input/output buffers
    
    return total;
}

void PacketReceiver::setQualityThresholds(float packet_loss_threshold, uint32_t max_latency_ms) {
    packet_loss_threshold_ = packet_loss_threshold;
    max_latency_ms_ = max_latency_ms;
}

void PacketReceiver::enableAdaptiveQuality(bool enable) {
    adaptive_quality_ = enable;
}

bool PacketReceiver::isAdaptiveQualityEnabled() const {
    return adaptive_quality_;
}

void PacketReceiver::handleThermalThrottling(int temperature) {
    thermal_throttling_ = (temperature >= 80);
    
    if (thermal_throttling_) {
        // Reduce processing load
        hardware_processing_ = false;
        std::cout << "[PacketReceiver] Thermal throttling activated at " << temperature << "°C" << std::endl;
    } else {
        // Restore normal processing
        hardware_processing_ = (rga_status_ == HardwareAccelStatus::HARDWARE_ACTIVE);
    }
}

void PacketReceiver::setPerformanceMode(bool high_performance) {
    if (high_performance && !thermal_throttling_) {
        hardware_processing_ = (rga_status_ == HardwareAccelStatus::HARDWARE_ACTIVE);
    } else {
        hardware_processing_ = false;
    }
}

// Private methods
void PacketReceiver::receiverWorker() {
    setCPUAffinity();
    
    std::cout << "[PacketReceiver] Receiver worker started" << std::endl;
    
    while (!should_stop_) {
        // TODO: Implement actual RTP packet reception
        // This is a skeleton implementation
        
        // Simulate packet reception
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        // Update statistics
        updateQueueStatistics();
    }
    
    std::cout << "[PacketReceiver] Receiver worker stopped" << std::endl;
}

void PacketReceiver::processorWorker() {
    setCPUAffinity();
    
    std::cout << "[PacketReceiver] Processor worker started" << std::endl;
    
    while (!should_stop_) {
        // TODO: Implement packet processing
        // This is a skeleton implementation
        
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        
        // Adaptive quality control
        if (adaptive_quality_) {
            adaptQualitySettings();
        }
    }
    
    std::cout << "[PacketReceiver] Processor worker stopped" << std::endl;
}

void PacketReceiver::processRTPPacket(const std::vector<uint8_t>& packet, Timestamp timestamp) {
    // Update statistics
    updatePacketStatistics(packet.size(), false, false);
    
    // Process with hardware if available
    if (hardware_processing_ && rga_status_ == HardwareAccelStatus::HARDWARE_ACTIVE) {
        std::vector<uint8_t> processed_packet;
        if (processWithRGA(packet, processed_packet)) {
            statistics_.hardware_accelerated_count++;
        } else {
            statistics_.software_fallback_count++;
        }
    }
    
    // Add to jitter buffer
    if (jitter_buffer_) {
        // Extract sequence number (simplified)
        uint32_t sequence = 0;  // TODO: Extract from RTP header
        jitter_buffer_->addPacket(packet, sequence, timestamp);
    }
    
    // Invoke callback
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (packet_callback_) {
        packet_callback_(packet, timestamp);
    }
}

void PacketReceiver::processRTCPPacket(const std::vector<uint8_t>& packet) {
    // TODO: Implement RTCP processing
    // Update statistics based on RTCP feedback
}

void PacketReceiver::updateJitterBuffer() {
    if (!jitter_buffer_) return;
    
    // Update jitter statistics
    float current_jitter = jitter_buffer_->getCurrentJitterMs();
    updateJitterStatistics(current_jitter);
}

void PacketReceiver::adaptQualitySettings() {
    if (!adaptive_quality_) return;
    
    // Check packet loss rate
    float loss_rate = static_cast<float>(statistics_.packets_lost) / 
                     std::max(1UL, statistics_.packets_received.load());
    
    if (loss_rate > packet_loss_threshold_) {
        // Increase jitter buffer size
        if (jitter_config_.initial_size_ms < jitter_config_.max_size_ms) {
            jitter_config_.initial_size_ms += 50;
            if (jitter_buffer_) {
                jitter_buffer_->updateConfig(jitter_config_);
            }
        }
    }
}

void PacketReceiver::handlePacketLoss() {
    statistics_.packets_lost++;
    
    // Handle packet loss recovery
    if (adaptive_quality_) {
        adaptQualitySettings();
    }
}

void PacketReceiver::optimizeForRK3588() {
    // Set CPU affinity to specific cores
    setCPUAffinity();
    
    // Configure hardware acceleration
    configureHardwareAcceleration();
}

void PacketReceiver::configureHardwareAcceleration() {
    // Initialize RGA if enabled
    if (config_.use_rga_scaler) {
        rga_status_ = HardwareAccelStatus::DISABLED;
    }
    
    // Initialize DMABUF if enabled
    if (config_.use_dmabuf_zerocopy) {
        dmabuf_enabled_ = false;
    }
}

void PacketReceiver::setCPUAffinity() {
    // Set CPU affinity to RK3588 efficiency cores (2-3)
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(2, &cpuset);  // Cortex-A55 core
    CPU_SET(3, &cpuset);  // Cortex-A55 core
    
    if (sched_setaffinity(0, sizeof(cpuset), &cpuset) != 0) {
        std::cout << "[PacketReceiver] Warning: Failed to set CPU affinity" << std::endl;
    }
}

bool PacketReceiver::initializeRGAScaler() {
    // TODO: Implement RGA scaler initialization
    rga_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
    return false;
}

void PacketReceiver::cleanupRGAScaler() {
    // TODO: Implement RGA scaler cleanup
    rga_status_ = HardwareAccelStatus::DISABLED;
}

bool PacketReceiver::initializeDMABuf() {
    // TODO: Implement DMABUF initialization
    dmabuf_enabled_ = false;
    return false;
}

void PacketReceiver::cleanupDMABuf() {
    // TODO: Implement DMABUF cleanup
    dmabuf_enabled_ = false;
}

bool PacketReceiver::processWithRGA(const std::vector<uint8_t>& input, std::vector<uint8_t>& output) {
    // TODO: Implement RGA processing
    output = input;  // Passthrough for now
    return false;
}

void PacketReceiver::updatePacketStatistics(size_t packet_size, bool is_duplicate, bool is_out_of_order) {
    if (!is_duplicate) {
        statistics_.packets_received++;
        statistics_.bytes_received += packet_size;
    } else {
        statistics_.duplicate_packets++;
    }
    
    if (is_out_of_order) {
        statistics_.out_of_order_packets++;
    }
}

void PacketReceiver::updateJitterStatistics(float jitter_ms) {
    statistics_.average_jitter_ms = jitter_ms;
}

void PacketReceiver::updateQueueStatistics() {
    if (jitter_buffer_) {
        statistics_.current_queue_depth = jitter_buffer_->getQueueDepth();
    }
}

void PacketReceiver::handleReceiveError(ErrorCategory category, const std::string& message) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (error_callback_) {
        error_callback_("", category, message);
    }
}

void PacketReceiver::handleHardwareError(const std::string& component, const std::string& error) {
    std::cout << "[PacketReceiver] Hardware error in " << component << ": " << error << std::endl;
    
    if (component == "RGA") {
        rga_status_ = HardwareAccelStatus::HARDWARE_ERROR;
        hardware_processing_ = false;
    }
}

bool PacketReceiver::validateConfig() const {
    return config_.isValid() &&
           jitter_config_.initial_size_ms > 0 &&
           jitter_config_.max_size_ms >= jitter_config_.initial_size_ms;
}

bool PacketReceiver::checkHardwareCapabilities() const {
    // TODO: Implement hardware capability checking
    return true;
}

// HardwareJitterBuffer implementation
HardwareJitterBuffer::HardwareJitterBuffer(const JitterBufferConfig& config)
    : config_(config)
    , overrun_count_(0)
    , underrun_count_(0)
    , current_jitter_(0.0f)
    , rga_enabled_(false)
    , dmabuf_enabled_(false) {

    std::cout << "[HardwareJitterBuffer] Initialized with " << config_.initial_size_ms << "ms buffer" << std::endl;
}

HardwareJitterBuffer::~HardwareJitterBuffer() {
    flush();
    std::cout << "[HardwareJitterBuffer] Destroyed" << std::endl;
}

bool HardwareJitterBuffer::addPacket(const std::vector<uint8_t>& packet, uint32_t sequence, Timestamp timestamp) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    // Check buffer capacity
    if (buffer_.size() >= static_cast<size_t>(config_.max_size_ms / 10)) {  // Rough estimate
        overrun_count_++;
        if (!config_.adaptive_sizing) {
            return false;  // Drop packet
        }

        // Remove oldest packet
        buffer_.pop();
    }

    // Create buffer entry
    BufferEntry entry;
    entry.data = packet;
    entry.sequence = sequence;
    entry.timestamp = timestamp;
    entry.processed = false;

    // Process with hardware if enabled
    if (rga_enabled_) {
        processWithHardware(entry);
    }

    buffer_.push(entry);
    return true;
}

bool HardwareJitterBuffer::getNextPacket(std::vector<uint8_t>& packet, Timestamp& timestamp) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    if (buffer_.empty()) {
        underrun_count_++;
        return false;
    }

    auto entry = buffer_.front();
    buffer_.pop();

    packet = std::move(entry.data);
    timestamp = entry.timestamp;

    return true;
}

void HardwareJitterBuffer::flush() {
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    while (!buffer_.empty()) {
        buffer_.pop();
    }

    overrun_count_ = 0;
    underrun_count_ = 0;
    current_jitter_ = 0.0f;
}

void HardwareJitterBuffer::updateConfig(const JitterBufferConfig& config) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    config_ = config;
}

void HardwareJitterBuffer::setTargetLatency(int latency_ms) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    config_.target_latency_ms = latency_ms;
}

void HardwareJitterBuffer::enableAdaptiveSizing(bool enable) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    config_.adaptive_sizing = enable;
}

uint32_t HardwareJitterBuffer::getQueueDepth() const {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    return static_cast<uint32_t>(buffer_.size());
}

float HardwareJitterBuffer::getCurrentJitterMs() const {
    return current_jitter_;
}

uint32_t HardwareJitterBuffer::getOverrunCount() const {
    return overrun_count_;
}

uint32_t HardwareJitterBuffer::getUnderrunCount() const {
    return underrun_count_;
}

size_t HardwareJitterBuffer::getMemoryUsage() const {
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    size_t total = sizeof(*this);
    total += buffer_.size() * (sizeof(BufferEntry) + 1500);  // Estimate 1500 bytes per packet

    return total;
}

bool HardwareJitterBuffer::enableRGAProcessing(bool enable) {
    rga_enabled_ = enable;
    return true;  // TODO: Implement actual RGA initialization
}

bool HardwareJitterBuffer::enableDMABuf(bool enable) {
    dmabuf_enabled_ = enable;
    return true;  // TODO: Implement actual DMABUF initialization
}

void HardwareJitterBuffer::adaptBufferSize() {
    if (!config_.adaptive_sizing) return;

    // Adapt buffer size based on jitter and packet loss
    float target_jitter = static_cast<float>(config_.target_latency_ms);

    if (current_jitter_ > target_jitter * 1.5f) {
        // Increase buffer size
        config_.initial_size_ms = std::min(config_.initial_size_ms + 50, config_.max_size_ms);
    } else if (current_jitter_ < target_jitter * 0.5f) {
        // Decrease buffer size
        config_.initial_size_ms = std::max(config_.initial_size_ms - 25, config_.min_size_ms);
    }
}

float HardwareJitterBuffer::calculateJitter(Timestamp current, Timestamp previous) {
    auto diff = std::chrono::duration_cast<std::chrono::microseconds>(current - previous);
    return static_cast<float>(diff.count()) / 1000.0f;  // Convert to milliseconds
}

bool HardwareJitterBuffer::shouldDropPacket(const BufferEntry& entry) {
    // TODO: Implement packet dropping logic based on latency and importance
    return false;
}

void HardwareJitterBuffer::processWithHardware(BufferEntry& entry) {
    if (!rga_enabled_) return;

    // TODO: Implement hardware processing with RGA
    entry.processed = true;
}

} // namespace rtsp
} // namespace aibox
