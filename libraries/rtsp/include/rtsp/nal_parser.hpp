#pragma once

#include "rtsp_types.hpp"
#include "rtsp_config.hpp"
#include <memory>
#include <vector>
#include <atomic>
#include <mutex>
#include <unordered_map>
#include <thread>

namespace aibox {
namespace rtsp {

// Forward declarations
class HardwareNALProcessor;

// NAL unit parsing statistics (non-atomic for return values)
struct NALParsingStatistics {
    uint64_t nal_units_parsed = 0;
    uint64_t sps_units_found = 0;
    uint64_t pps_units_found = 0;
    uint64_t idr_frames_found = 0;
    uint64_t p_frames_found = 0;
    uint64_t b_frames_found = 0;
    uint32_t parsing_errors = 0;
    uint32_t invalid_nal_units = 0;
    uint32_t hardware_accelerated_count = 0;
    uint32_t software_fallback_count = 0;

    double getHardwareAccelRate() const {
        uint64_t total = hardware_accelerated_count + software_fallback_count;
        return total > 0 ? static_cast<double>(hardware_accelerated_count) / total : 0.0;
    }
};

// Internal atomic statistics for thread-safe updates
struct AtomicNALParsingStatistics {
    std::atomic<uint64_t> nal_units_parsed{0};
    std::atomic<uint64_t> sps_units_found{0};
    std::atomic<uint64_t> pps_units_found{0};
    std::atomic<uint64_t> idr_frames_found{0};
    std::atomic<uint64_t> p_frames_found{0};
    std::atomic<uint64_t> b_frames_found{0};
    std::atomic<uint32_t> parsing_errors{0};
    std::atomic<uint32_t> invalid_nal_units{0};
    std::atomic<uint32_t> hardware_accelerated_count{0};
    std::atomic<uint32_t> software_fallback_count{0};
    
    void reset() {
        nal_units_parsed = 0;
        sps_units_found = 0;
        pps_units_found = 0;
        idr_frames_found = 0;
        p_frames_found = 0;
        b_frames_found = 0;
        parsing_errors = 0;
        invalid_nal_units = 0;
        hardware_accelerated_count = 0;
        software_fallback_count = 0;
    }
};

// NAL unit parsing configuration
struct NALParsingConfig {
    // Hardware acceleration
    bool enable_mpp_parsing = true;
    bool enable_hardware_validation = true;
    bool enable_zero_copy = true;
    
    // Parsing options
    bool validate_nal_headers = true;
    bool extract_metadata = true;
    bool detect_frame_types = true;
    bool parse_sps_pps = true;
    
    // Performance tuning
    size_t max_nal_size_bytes = 2 * 1024 * 1024;  // 2MB max NAL unit
    int parsing_timeout_ms = 100;
    bool enable_parallel_parsing = true;
    int worker_thread_count = 2;
    
    // Error handling
    bool strict_validation = false;
    bool skip_invalid_nal_units = true;
    int max_consecutive_errors = 10;
    
    // Memory management
    size_t buffer_pool_size = 32;
    size_t max_memory_usage_mb = 100;
};

// Video stream information extracted from NAL units
struct VideoStreamInfo {
    VideoCodec codec = VideoCodec::UNKNOWN;
    Resolution resolution{0, 0};
    FrameRate framerate{30, 1};
    uint32_t bitrate_kbps = 0;
    uint32_t profile = 0;
    uint32_t level = 0;
    bool has_sps = false;
    bool has_pps = false;
    bool has_vps = false;  // H.265 only
    
    // SPS/PPS data for decoder initialization
    std::vector<uint8_t> sps_data;
    std::vector<uint8_t> pps_data;
    std::vector<uint8_t> vps_data;  // H.265 only
    
    bool isValid() const {
        return codec != VideoCodec::UNKNOWN && 
               resolution.isValid() && 
               has_sps && has_pps;
    }
};

/**
 * @brief NAL Unit Parser optimized for RK3588 hardware acceleration
 * 
 * Parses H.264/H.265 NAL units from RTP packets with hardware acceleration
 * support via MPP decoder and RGA processing.
 */
class NALParser {
public:
    // Constructor
    explicit NALParser(const NALParsingConfig& config = NALParsingConfig{});
    
    // Destructor
    ~NALParser();
    
    // Non-copyable, movable
    NALParser(const NALParser&) = delete;
    NALParser& operator=(const NALParser&) = delete;
    NALParser(NALParser&&) = default;
    NALParser& operator=(NALParser&&) = default;
    
    // Configuration management
    void updateConfig(const NALParsingConfig& new_config);
    const NALParsingConfig& getConfig() const;
    
    // Parsing operations
    std::vector<NALUnit> parseRTPPayload(const std::vector<uint8_t>& payload, 
                                        const StreamId& stream_id,
                                        Timestamp timestamp);
    
    bool parseNALUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit);
    NALUnitType detectNALUnitType(const std::vector<uint8_t>& data);
    bool isKeyframe(const NALUnit& nal_unit);
    
    // Stream information extraction
    bool extractStreamInfo(const std::vector<NALUnit>& nal_units, VideoStreamInfo& info);
    bool updateStreamInfo(const NALUnit& nal_unit, VideoStreamInfo& info);
    VideoStreamInfo getCurrentStreamInfo(const StreamId& stream_id) const;
    
    // Hardware acceleration control
    bool enableHardwareAcceleration(bool enable);
    bool isHardwareAccelerationEnabled() const;
    HardwareAccelStatus getHardwareStatus() const;
    
    // Callbacks
    void setNALUnitCallback(std::function<void(const NALUnit&)> callback);
    void setStreamInfoCallback(std::function<void(const StreamId&, const VideoStreamInfo&)> callback);
    void setErrorCallback(StreamErrorCallback callback);
    
    // Statistics and monitoring
    NALParsingStatistics getStatistics() const;
    size_t getMemoryUsage() const;
    uint32_t getActiveStreamCount() const;
    
    // Thermal management
    void handleThermalThrottling(int temperature);
    void setPerformanceMode(bool high_performance);
    
private:
    // Configuration
    NALParsingConfig config_;
    mutable std::mutex config_mutex_;
    
    // Hardware acceleration components
    std::unique_ptr<HardwareNALProcessor> hardware_processor_;
    std::atomic<HardwareAccelStatus> hardware_status_;
    
    // Stream information tracking
    std::unordered_map<StreamId, VideoStreamInfo> stream_info_map_;
    mutable std::mutex stream_info_mutex_;
    
    // Statistics (using atomic types internally)
    mutable AtomicNALParsingStatistics statistics_;
    mutable std::mutex stats_mutex_;
    
    // Threading
    std::vector<std::unique_ptr<std::thread>> worker_threads_;
    std::atomic<bool> should_stop_;
    
    // Callbacks
    std::function<void(const NALUnit&)> nal_callback_;
    std::function<void(const StreamId&, const VideoStreamInfo&)> stream_info_callback_;
    StreamErrorCallback error_callback_;
    std::mutex callback_mutex_;
    
    // Internal methods
    void initializeHardwareAcceleration();
    void cleanupHardwareAcceleration();
    void optimizeForRK3588();
    void setCPUAffinity();
    
    // NAL unit parsing helpers
    bool parseH264NALUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit);
    bool parseH265NALUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit);
    bool validateNALHeader(const std::vector<uint8_t>& data);
    
    // Stream information helpers
    bool parseSPS(const std::vector<uint8_t>& sps_data, VideoStreamInfo& info);
    bool parsePPS(const std::vector<uint8_t>& pps_data, VideoStreamInfo& info);
    bool parseVPS(const std::vector<uint8_t>& vps_data, VideoStreamInfo& info);
    
    // Hardware processing
    bool processWithMPP(const std::vector<uint8_t>& data, NALUnit& nal_unit);
    bool processWithSoftware(const std::vector<uint8_t>& data, NALUnit& nal_unit);
    
    // Error handling
    void handleParsingError(ErrorCategory category, const std::string& message);
    void handleHardwareError(const std::string& component, const std::string& error);
    
    // Statistics helpers
    void updateParsingStatistics(const NALUnit& nal_unit, bool hardware_accelerated);
    void updateStreamStatistics(const StreamId& stream_id, const VideoStreamInfo& info);
    
    // Validation
    bool validateConfig() const;
    bool checkHardwareCapabilities() const;

    // Helper methods
    size_t findNextStartCode(const std::vector<uint8_t>& data, size_t start_pos);
};

/**
 * @brief Hardware-accelerated NAL processor for RK3588
 * 
 * Provides hardware acceleration for NAL unit parsing using MPP decoder.
 */
class HardwareNALProcessor {
public:
    explicit HardwareNALProcessor(const NALParsingConfig& config);
    ~HardwareNALProcessor();
    
    // Hardware operations
    bool initialize();
    void cleanup();
    bool isInitialized() const;
    
    // Processing operations
    bool processNALUnit(const std::vector<uint8_t>& input, NALUnit& output);
    bool validateNALUnit(const std::vector<uint8_t>& data);
    
    // Status and monitoring
    HardwareAccelStatus getStatus() const;
    size_t getMemoryUsage() const;
    uint32_t getProcessedCount() const;
    
private:
    NALParsingConfig config_;
    std::atomic<bool> initialized_;
    std::atomic<HardwareAccelStatus> status_;
    std::atomic<uint32_t> processed_count_;
    
    // Hardware-specific implementation details
    void* mpp_context_;  // MPP decoder context
    void* rga_context_;  // RGA context for memory operations
    
    // Internal methods
    bool initializeMPP();
    void cleanupMPP();
    bool initializeRGA();
    void cleanupRGA();
};

} // namespace rtsp
} // namespace aibox
