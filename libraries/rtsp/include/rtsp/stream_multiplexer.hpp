#pragma once

#include "rtsp_types.hpp"
#include "rtsp_config.hpp"
#include <memory>
#include <vector>
#include <unordered_map>
#include <queue>
#include <atomic>
#include <mutex>
#include <thread>

namespace aibox {
namespace rtsp {

// Forward declarations
class ConnectionManager;
class PacketReceiver;
class NALParser;
class ThreadSafeQueue;

// Stream multiplexer statistics
struct MultiplexerStatistics {
    std::atomic<uint32_t> active_streams{0};
    std::atomic<uint32_t> total_streams{0};
    std::atomic<uint64_t> total_packets_processed{0};
    std::atomic<uint64_t> total_bytes_processed{0};
    std::atomic<uint32_t> queue_overruns{0};
    std::atomic<uint32_t> dropped_packets{0};
    std::atomic<uint32_t> connection_errors{0};
    std::atomic<uint32_t> parsing_errors{0};
    std::atomic<float> average_latency_ms{0.0f};
    std::atomic<size_t> memory_usage_bytes{0};
    
    void reset() {
        active_streams = 0;
        total_streams = 0;
        total_packets_processed = 0;
        total_bytes_processed = 0;
        queue_overruns = 0;
        dropped_packets = 0;
        connection_errors = 0;
        parsing_errors = 0;
        average_latency_ms = 0.0f;
        memory_usage_bytes = 0;
    }
    
    double getPacketDropRate() const {
        uint64_t total = total_packets_processed + dropped_packets;
        return total > 0 ? static_cast<double>(dropped_packets) / total : 0.0;
    }
};

// Stream priority queue configuration
struct StreamQueueConfig {
    size_t high_priority_size = 200;
    size_t medium_priority_size = 150;
    size_t low_priority_size = 100;
    bool enable_priority_scheduling = true;
    bool enable_adaptive_sizing = true;
    int queue_timeout_ms = 1000;
    float memory_pressure_threshold = 0.8f;
};

// Stream management configuration
struct StreamManagementConfig {
    // Resource limits
    int max_concurrent_streams = 16;
    size_t max_memory_usage_mb = 2000;
    float cpu_usage_limit_percent = 80.0f;
    
    // Performance tuning
    bool enable_load_balancing = true;
    bool enable_adaptive_quality = true;
    bool enable_thermal_management = true;
    int worker_thread_count = 4;
    
    // Queue management
    StreamQueueConfig queue_config;
    
    // Error handling
    int max_connection_retries = 3;
    int retry_delay_ms = 1000;
    bool auto_remove_failed_streams = true;
    
    // Monitoring
    int statistics_update_interval_ms = 1000;
    bool enable_performance_monitoring = true;
};

// Stream information
struct StreamInfo {
    StreamId id;
    RTSPConnectionConfig config;
    ConnectionState state;
    StreamPriority priority;
    Timestamp last_activity;
    size_t queue_depth;
    uint64_t packets_processed;
    uint32_t error_count;
    bool enabled;
    
    StreamInfo() : 
        state(ConnectionState::DISCONNECTED),
        priority(StreamPriority::MEDIUM),
        queue_depth(0),
        packets_processed(0),
        error_count(0),
        enabled(true) {}
};

/**
 * @brief Stream Multiplexer for managing multiple RTSP streams on RK3588
 * 
 * Coordinates multiple RTSP connections, manages resource allocation,
 * and provides unified stream management with hardware acceleration.
 */
class StreamMultiplexer {
public:
    // Constructor
    explicit StreamMultiplexer(const StreamManagementConfig& config = StreamManagementConfig{});
    
    // Destructor
    ~StreamMultiplexer();
    
    // Non-copyable, movable
    StreamMultiplexer(const StreamMultiplexer&) = delete;
    StreamMultiplexer& operator=(const StreamMultiplexer&) = delete;
    StreamMultiplexer(StreamMultiplexer&&) = default;
    StreamMultiplexer& operator=(StreamMultiplexer&&) = default;
    
    // Lifecycle management
    bool start();
    void stop();
    bool isRunning() const;
    
    // Configuration management
    void updateConfig(const StreamManagementConfig& new_config);
    const StreamManagementConfig& getConfig() const;
    
    // Stream management
    bool addStream(const StreamId& id, const RTSPConnectionConfig& config);
    bool removeStream(const StreamId& id);
    bool enableStream(const StreamId& id, bool enable);
    bool updateStreamConfig(const StreamId& id, const RTSPConnectionConfig& config);
    
    // Stream control
    bool connectStream(const StreamId& id);
    bool disconnectStream(const StreamId& id);
    bool reconnectStream(const StreamId& id);
    void connectAllStreams();
    void disconnectAllStreams();
    
    // Stream information
    std::vector<StreamId> getStreamIds() const;
    std::vector<StreamInfo> getStreamInfos() const;
    StreamInfo getStreamInfo(const StreamId& id) const;
    ConnectionState getStreamState(const StreamId& id) const;
    
    // Priority management
    bool setStreamPriority(const StreamId& id, StreamPriority priority);
    StreamPriority getStreamPriority(const StreamId& id) const;
    void rebalanceStreams();
    
    // Data callbacks
    void setNALUnitCallback(std::function<void(const StreamId&, const NALUnit&)> callback);
    void setStreamEventCallback(std::function<void(const StreamId&, const std::string&)> callback);
    void setErrorCallback(std::function<void(const StreamId&, ErrorCategory, const std::string&)> callback);
    
    // Statistics and monitoring
    MultiplexerStatistics getStatistics() const;
    SystemHealth getSystemHealth() const;
    size_t getTotalMemoryUsage() const;
    uint32_t getActiveStreamCount() const;
    
    // Resource management
    bool checkResourceAvailability(const RTSPConnectionConfig& config) const;
    void optimizeResourceUsage();
    void handleMemoryPressure();
    
    // Thermal management
    void handleThermalThrottling(int temperature);
    void setPerformanceMode(bool high_performance);
    
    // Load balancing
    void enableLoadBalancing(bool enable);
    bool isLoadBalancingEnabled() const;
    void redistributeLoad();
    
private:
    // Configuration
    StreamManagementConfig config_;
    mutable std::mutex config_mutex_;
    
    // Stream management
    std::unordered_map<StreamId, std::unique_ptr<ConnectionManager>> connections_;
    std::unordered_map<StreamId, std::unique_ptr<PacketReceiver>> receivers_;
    std::unordered_map<StreamId, std::unique_ptr<NALParser>> parsers_;
    std::unordered_map<StreamId, StreamInfo> stream_infos_;
    mutable std::mutex streams_mutex_;
    
    // Priority queues
    std::unique_ptr<ThreadSafeQueue<NALUnit>> high_priority_queue_;
    std::unique_ptr<ThreadSafeQueue<NALUnit>> medium_priority_queue_;
    std::unique_ptr<ThreadSafeQueue<NALUnit>> low_priority_queue_;
    
    // Threading
    std::vector<std::unique_ptr<std::thread>> worker_threads_;
    std::unique_ptr<std::thread> management_thread_;
    std::unique_ptr<std::thread> monitoring_thread_;
    std::atomic<bool> should_stop_;
    std::atomic<bool> is_running_;
    
    // Statistics
    mutable MultiplexerStatistics statistics_;
    mutable std::mutex stats_mutex_;
    
    // Resource monitoring
    std::atomic<size_t> total_memory_usage_;
    std::atomic<float> cpu_usage_;
    std::atomic<int> soc_temperature_;
    std::atomic<bool> thermal_throttling_;
    
    // Callbacks
    std::function<void(const StreamId&, const NALUnit&)> nal_callback_;
    std::function<void(const StreamId&, const std::string&)> event_callback_;
    std::function<void(const StreamId&, ErrorCategory, const std::string&)> error_callback_;
    std::mutex callback_mutex_;
    
    // Internal methods
    void initializeQueues();
    void cleanupQueues();
    void optimizeForRK3588();
    void setCPUAffinity();
    
    // Worker threads
    void managementWorker();
    void monitoringWorker();
    void processingWorker(int worker_id);
    
    // Stream lifecycle
    bool createStreamComponents(const StreamId& id, const RTSPConnectionConfig& config);
    void destroyStreamComponents(const StreamId& id);
    bool configureStreamComponents(const StreamId& id, const RTSPConnectionConfig& config);
    
    // Queue management
    bool enqueueNALUnit(const StreamId& stream_id, const NALUnit& nal_unit);
    bool dequeueNALUnit(NALUnit& nal_unit, StreamId& stream_id);
    void processQueueOverrun(StreamPriority priority);
    
    // Resource management
    bool allocateResources(const StreamId& id, const RTSPConnectionConfig& config);
    void deallocateResources(const StreamId& id);
    bool checkMemoryLimit(size_t additional_memory) const;
    bool checkCPULimit() const;
    
    // Load balancing
    void balanceStreamLoad();
    void adjustStreamPriorities();
    void redistributeResources();
    
    // Error handling
    void handleStreamError(const StreamId& id, ErrorCategory category, const std::string& message);
    void handleSystemError(ErrorCategory category, const std::string& message);
    bool shouldRetryStream(const StreamId& id) const;
    
    // Statistics helpers
    void updateStatistics();
    void updateStreamStatistics(const StreamId& id);
    void updateSystemStatistics();
    
    // Validation
    bool validateConfig() const;
    bool validateStreamConfig(const RTSPConnectionConfig& config) const;
    bool checkHardwareCapabilities() const;
};

/**
 * @brief Thread-safe priority queue for NAL units
 * 
 * Implements a lock-free queue optimized for RK3588 performance.
 */
template<typename T>
class ThreadSafeQueue {
public:
    explicit ThreadSafeQueue(size_t max_size = 1000);
    ~ThreadSafeQueue();
    
    // Queue operations
    bool enqueue(const T& item, int timeout_ms = -1);
    bool dequeue(T& item, int timeout_ms = -1);
    bool tryEnqueue(const T& item);
    bool tryDequeue(T& item);
    
    // Queue status
    size_t size() const;
    size_t capacity() const;
    bool empty() const;
    bool full() const;
    void clear();
    
    // Configuration
    void setMaxSize(size_t max_size);
    void enableAdaptiveSizing(bool enable);
    
private:
    std::queue<T> queue_;
    mutable std::mutex mutex_;
    std::condition_variable not_empty_;
    std::condition_variable not_full_;
    size_t max_size_;
    std::atomic<bool> adaptive_sizing_;
    
    // Statistics
    std::atomic<uint64_t> enqueue_count_;
    std::atomic<uint64_t> dequeue_count_;
    std::atomic<uint32_t> overrun_count_;
};

} // namespace rtsp
} // namespace aibox
