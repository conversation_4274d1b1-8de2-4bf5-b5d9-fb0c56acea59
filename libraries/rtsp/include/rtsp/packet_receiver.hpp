#pragma once

#include "rtsp_types.hpp"
#include "rtsp_config.hpp"
#include <memory>
#include <atomic>
#include <thread>
#include <queue>
#include <mutex>

namespace aibox {
namespace rtsp {

// Forward declarations
class HardwareJitterBuffer;

// Packet statistics (non-atomic for return values)
struct PacketStatistics {
    uint64_t packets_received = 0;
    uint64_t packets_lost = 0;
    uint64_t bytes_received = 0;
    uint32_t out_of_order_packets = 0;
    uint32_t duplicate_packets = 0;
    uint32_t jitter_buffer_overruns = 0;
    uint32_t jitter_buffer_underruns = 0;
    float average_jitter_ms = 0.0f;
    uint32_t current_queue_depth = 0;
    uint32_t hardware_accelerated_count = 0;
    uint32_t software_fallback_count = 0;

    double getPacketLossRate() const {
        uint64_t total = packets_received + packets_lost;
        return total > 0 ? static_cast<double>(packets_lost) / total : 0.0;
    }
};

// Internal atomic statistics for thread-safe updates
struct AtomicPacketStatistics {
    std::atomic<uint64_t> packets_received{0};
    std::atomic<uint64_t> packets_lost{0};
    std::atomic<uint64_t> bytes_received{0};
    std::atomic<uint32_t> out_of_order_packets{0};
    std::atomic<uint32_t> duplicate_packets{0};
    std::atomic<uint32_t> jitter_buffer_overruns{0};
    std::atomic<uint32_t> jitter_buffer_underruns{0};
    std::atomic<float> average_jitter_ms{0.0f};
    std::atomic<uint32_t> current_queue_depth{0};
    std::atomic<uint32_t> hardware_accelerated_count{0};
    std::atomic<uint32_t> software_fallback_count{0};
    
    void reset() {
        packets_received = 0;
        packets_lost = 0;
        bytes_received = 0;
        out_of_order_packets = 0;
        duplicate_packets = 0;
        jitter_buffer_overruns = 0;
        jitter_buffer_underruns = 0;
        average_jitter_ms = 0.0f;
        current_queue_depth = 0;
        hardware_accelerated_count = 0;
        software_fallback_count = 0;
    }
};

// Jitter buffer configuration
struct JitterBufferConfig {
    int initial_size_ms = 200;
    int max_size_ms = 1000;
    int min_size_ms = 50;
    bool adaptive_sizing = true;
    int target_latency_ms = 100;
    float packet_loss_threshold = 0.05f;
    bool enable_reordering = true;
    int max_reorder_delay_ms = 100;
};

/**
 * @brief Packet Receiver optimized for RK3588 hardware acceleration
 * 
 * Handles RTP packet reception, RTCP processing, and jitter buffer management
 * with hardware acceleration support via RGA and DMABUF.
 */
class PacketReceiver {
public:
    // Constructor
    explicit PacketReceiver(const RTSPConnectionConfig& config);
    
    // Destructor
    ~PacketReceiver();
    
    // Non-copyable, movable
    PacketReceiver(const PacketReceiver&) = delete;
    PacketReceiver& operator=(const PacketReceiver&) = delete;
    PacketReceiver(PacketReceiver&&) = default;
    PacketReceiver& operator=(PacketReceiver&&) = default;
    
    // Lifecycle management
    bool startReceiving();
    void stopReceiving();
    bool isReceiving() const;
    
    // Configuration
    void updateConfig(const RTSPConnectionConfig& new_config);
    void configureJitterBuffer(const JitterBufferConfig& config);
    const JitterBufferConfig& getJitterBufferConfig() const;
    
    // Transport configuration
    void setTransportMode(TransportProtocol mode);
    TransportProtocol getTransportMode() const;
    
    // Hardware acceleration control
    bool enableRGAScaling(bool enable);
    bool enableDMABufZeroCopy(bool enable);
    HardwareAccelStatus getRGAStatus() const;
    bool isDMABufEnabled() const;
    
    // Data callbacks
    void setPacketCallback(std::function<void(const std::vector<uint8_t>&, Timestamp)> callback);
    void setNALUnitCallback(std::function<void(const NALUnit&)> callback);
    void setErrorCallback(StreamErrorCallback callback);
    
    // Statistics and monitoring
    PacketStatistics getStatistics() const;
    float getCurrentJitterMs() const;
    uint32_t getQueueDepth() const;
    size_t getMemoryUsage() const;
    
    // Quality control
    void setQualityThresholds(float packet_loss_threshold, uint32_t max_latency_ms);
    void enableAdaptiveQuality(bool enable);
    bool isAdaptiveQualityEnabled() const;
    
    // Thermal management
    void handleThermalThrottling(int temperature);
    void setPerformanceMode(bool high_performance);
    
private:
    // Configuration
    RTSPConnectionConfig config_;
    JitterBufferConfig jitter_config_;
    std::atomic<TransportProtocol> transport_mode_;
    
    // Components
    std::unique_ptr<HardwareJitterBuffer> jitter_buffer_;
    
    // Threading
    std::unique_ptr<std::thread> receiver_thread_;
    std::unique_ptr<std::thread> processor_thread_;
    std::atomic<bool> should_stop_;
    std::atomic<bool> is_receiving_;
    
    // Hardware acceleration state
    std::atomic<HardwareAccelStatus> rga_status_;
    std::atomic<bool> dmabuf_enabled_;
    std::atomic<bool> hardware_processing_;
    
    // Statistics (using atomic types internally)
    mutable AtomicPacketStatistics statistics_;
    mutable std::mutex stats_mutex_;
    
    // Quality control
    std::atomic<bool> adaptive_quality_;
    std::atomic<float> packet_loss_threshold_;
    std::atomic<uint32_t> max_latency_ms_;
    std::atomic<bool> thermal_throttling_;
    
    // Callbacks
    std::function<void(const std::vector<uint8_t>&, Timestamp)> packet_callback_;
    std::function<void(const NALUnit&)> nal_callback_;
    StreamErrorCallback error_callback_;
    std::mutex callback_mutex_;
    
    // Internal methods
    void receiverWorker();
    void processorWorker();
    void processRTPPacket(const std::vector<uint8_t>& packet, Timestamp timestamp);
    void processRTCPPacket(const std::vector<uint8_t>& packet);
    void updateJitterBuffer();
    void adaptQualitySettings();
    void handlePacketLoss();
    void optimizeForRK3588();
    void configureHardwareAcceleration();
    void setCPUAffinity();
    
    // Hardware-specific methods
    bool initializeRGAScaler();
    void cleanupRGAScaler();
    bool initializeDMABuf();
    void cleanupDMABuf();
    bool processWithRGA(const std::vector<uint8_t>& input, std::vector<uint8_t>& output);
    
    // Statistics helpers
    void updatePacketStatistics(size_t packet_size, bool is_duplicate, bool is_out_of_order);
    void updateJitterStatistics(float jitter_ms);
    void updateQueueStatistics();
    
    // Error handling
    void handleReceiveError(ErrorCategory category, const std::string& message);
    void handleHardwareError(const std::string& component, const std::string& error);
    
    // Validation
    bool validateConfig() const;
    bool checkHardwareCapabilities() const;
};

/**
 * @brief Hardware-accelerated jitter buffer for RK3588
 * 
 * Implements adaptive jitter buffer with RGA acceleration and DMABUF support.
 */
class HardwareJitterBuffer {
public:
    explicit HardwareJitterBuffer(const JitterBufferConfig& config);
    ~HardwareJitterBuffer();
    
    // Buffer management
    bool addPacket(const std::vector<uint8_t>& packet, uint32_t sequence, Timestamp timestamp);
    bool getNextPacket(std::vector<uint8_t>& packet, Timestamp& timestamp);
    void flush();
    
    // Configuration
    void updateConfig(const JitterBufferConfig& config);
    void setTargetLatency(int latency_ms);
    void enableAdaptiveSizing(bool enable);
    
    // Statistics
    uint32_t getQueueDepth() const;
    float getCurrentJitterMs() const;
    uint32_t getOverrunCount() const;
    uint32_t getUnderrunCount() const;
    size_t getMemoryUsage() const;
    
    // Hardware acceleration
    bool enableRGAProcessing(bool enable);
    bool enableDMABuf(bool enable);
    
private:
    struct BufferEntry {
        std::vector<uint8_t> data;
        uint32_t sequence;
        Timestamp timestamp;
        bool processed;
    };
    
    JitterBufferConfig config_;
    std::queue<BufferEntry> buffer_;
    mutable std::mutex buffer_mutex_;
    
    // Statistics
    std::atomic<uint32_t> overrun_count_;
    std::atomic<uint32_t> underrun_count_;
    std::atomic<float> current_jitter_;
    
    // Hardware acceleration
    std::atomic<bool> rga_enabled_;
    std::atomic<bool> dmabuf_enabled_;
    
    // Internal methods
    void adaptBufferSize();
    float calculateJitter(Timestamp current, Timestamp previous);
    bool shouldDropPacket(const BufferEntry& entry);
    void processWithHardware(BufferEntry& entry);
};

} // namespace rtsp
} // namespace aibox
