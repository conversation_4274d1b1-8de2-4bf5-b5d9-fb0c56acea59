# C-AIBOX Deployment Guide

This guide explains how to deploy the C-AIBOX application to an Orange Pi device using Docker containers.

## Prerequisites

### Local Development Machine
- Docker installed and running
- SSH access to the Orange Pi device
- `scp` and `ssh` commands available

### Orange Pi Device
- Orange Pi with ARM64 architecture
- Docker installed and running
- SSH server running
- Network connectivity to the development machine

## Quick Start

### 1. Automated Deployment

The simplest way to deploy is using the automated deployment script:

```bash
# Deploy to default Orange Pi (***************)
./scripts/deploy.sh

# Deploy to custom host
./scripts/deploy.sh --host ************* --user myuser
```

### 2. Manual Deployment Steps

If you prefer manual deployment or need to troubleshoot:

#### Step 1: Prepare the Orange Pi
```bash
# SSH into the Orange Pi
ssh orangepi@***************

# Create deployment directory
mkdir -p /home/<USER>/c-aibox-deploy/{data,logs,models}

# Ensure Docker is running
sudo systemctl start docker
sudo systemctl enable docker
```

#### Step 2: Transfer Source Files
```bash
# From your development machine
scp -r simple-server orangepi@***************:/home/<USER>/c-aibox-deploy/
scp -r simple-web-client orangepi@***************:/home/<USER>/c-aibox-deploy/
```

#### Step 3: Build Images on Orange Pi
```bash
# SSH into Orange Pi
ssh orangepi@***************

cd /home/<USER>/c-aibox-deploy

# Build server image
cd simple-server
docker build -t c-aibox-simple-server:latest .

# Build web client image
cd ../simple-web-client
docker build -t c-aibox-simple-web-client:latest .
```

#### Step 4: Deploy Services
```bash
# Create Docker network
docker network create c-aibox-network

# Start server
docker run -d --name c-aibox-simple-server \
  --network c-aibox-network \
  -p 8080:8080 \
  -v ./data:/app/data \
  -v ./logs:/app/logs \
  -v ./models:/app/models \
  c-aibox-simple-server:latest

# Start web client
docker run -d --name c-aibox-simple-web-client \
  --network c-aibox-network \
  -p 3000:3000 \
  -p 8081:80 \
  -v ./data:/app/data \
  -v ./logs:/app/logs \
  c-aibox-simple-web-client:latest
```

## Architecture

### Simple Deployment Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Orange Pi Device                         │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                Docker Network                           ││
│  │  ┌─────────────────────┐  ┌─────────────────────────────┐││
│  │  │  Simple Server      │  │  Simple Web Client          │││
│  │  │  (Node.js/Express)  │  │  (Nginx + HTML/JS)          │││
│  │  │  Port: 8080         │  │  Ports: 3000, 8081          │││
│  │  │  API Endpoints      │  │  Web Interface              │││
│  │  └─────────────────────┘  └─────────────────────────────┘││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### Container Details

#### Simple Server Container
- **Base Image**: `node:18-alpine`
- **Purpose**: Mock API server for testing
- **Ports**: 8080
- **Endpoints**:
  - `GET /health` - Health check
  - `GET /api/status` - Server status
  - `GET /api/models` - List available models
  - `POST /api/inference` - Run inference

#### Simple Web Client Container
- **Base Image**: `nginx:alpine`
- **Purpose**: Web interface for interacting with the server
- **Ports**: 3000 (direct), 8081 (nginx)
- **Features**:
  - Interactive web UI
  - API testing interface
  - Real-time status monitoring

## Access Points

After successful deployment, you can access the application at:

- **Server API**: http://***************:8080
- **Web Client**: http://***************:3000
- **Web Client (Nginx)**: http://***************:8081

## Testing the Deployment

### 1. Health Check
```bash
curl http://***************:8080/health
```
Expected response:
```json
{"status":"ok","service":"c-aibox-simple-server"}
```

### 2. API Status
```bash
curl http://***************:8080/api/status
```
Expected response:
```json
{"status":"running","message":"Simple C-AIBOX Server"}
```

### 3. Web Interface
Open http://***************:3000 in your browser and test the interactive interface.

## Troubleshooting

### Common Issues

#### 1. Connection Refused
```bash
# Check if containers are running
ssh orangepi@*************** "docker ps"

# Check container logs
ssh orangepi@*************** "docker logs c-aibox-simple-server"
ssh orangepi@*************** "docker logs c-aibox-simple-web-client"
```

#### 2. Architecture Mismatch
If you see "exec format error", it means you're trying to run x86_64 images on ARM64. Use the simple deployment which builds images directly on the Orange Pi.

#### 3. Port Conflicts
```bash
# Check what's using the ports
ssh orangepi@*************** "netstat -tulpn | grep :8080"

# Stop conflicting services
ssh orangepi@*************** "docker stop <container_name>"
```

### Cleanup

To completely remove the deployment:
```bash
# Stop and remove containers
ssh orangepi@*************** "docker rm -f c-aibox-simple-server c-aibox-simple-web-client"

# Remove images
ssh orangepi@*************** "docker rmi c-aibox-simple-server:latest c-aibox-simple-web-client:latest"

# Remove network
ssh orangepi@*************** "docker network rm c-aibox-network"

# Remove deployment directory
ssh orangepi@*************** "rm -rf /home/<USER>/c-aibox-deploy"
```

## Next Steps

1. **Add Real AI Models**: Replace the mock server with actual AI model inference
2. **Implement Authentication**: Add user authentication and authorization
3. **Add Monitoring**: Implement logging and monitoring solutions
4. **Scale Services**: Use Docker Swarm or Kubernetes for scaling
5. **Add HTTPS**: Configure SSL/TLS certificates for secure communication

## Support

For issues and questions:
1. Check the container logs for error messages
2. Verify network connectivity between containers
3. Ensure the Orange Pi has sufficient resources (CPU, memory, disk)
4. Check Docker daemon status on the Orange Pi
