[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:xvfb]
command=/usr/bin/Xvfb :0 -screen 0 %(ENV_VNC_RESOLUTION)sx%(ENV_VNC_DEPTH)s -ac +extension GLX +render -noreset
user=aibox
autostart=true
autorestart=true
priority=100
stdout_logfile=/var/log/supervisor/xvfb.log
stderr_logfile=/var/log/supervisor/xvfb.log

[program:fluxbox]
command=/usr/bin/fluxbox
user=aibox
environment=DISPLAY=":0"
autostart=true
autorestart=true
priority=200
stdout_logfile=/var/log/supervisor/fluxbox.log
stderr_logfile=/var/log/supervisor/fluxbox.log

[program:x11vnc]
command=/usr/bin/x11vnc -display :0 -nopw -listen localhost -xkb -ncache 10 -ncache_cr -forever -shared
user=aibox
environment=DISPLAY=":0"
autostart=true
autorestart=true
priority=300
stdout_logfile=/var/log/supervisor/x11vnc.log
stderr_logfile=/var/log/supervisor/x11vnc.log

[program:server]
command=/app/server --port %(ENV_SERVER_PORT)s --host %(ENV_SERVER_HOST)s
directory=/app
user=aibox
autostart=true
autorestart=true
priority=400
stdout_logfile=/var/log/supervisor/server.log
stderr_logfile=/var/log/supervisor/server.log

[program:client_app]
command=/app/client_app
directory=/app
user=aibox
environment=DISPLAY=":0",QT_X11_NO_MITSHM="1",QT_QPA_PLATFORM="xcb",QTWEBENGINE_DISABLE_SANDBOX="1",LIBGL_ALWAYS_INDIRECT="1"
autostart=true
autorestart=true
priority=500
stdout_logfile=/var/log/supervisor/client_app.log
stderr_logfile=/var/log/supervisor/client_app.log
