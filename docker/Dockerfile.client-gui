# Enhanced Dockerfile for C-AIBOX Client GUI with X11 and Web support
# Optimized for Orange Pi ARM64 deployment with flexible client options

FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install system dependencies for GUI applications and web server
RUN apt-get update && apt-get install -y \
    # Basic runtime dependencies
    libstdc++6 \
    libc6 \
    libgcc-s1 \
    ca-certificates \
    curl \
    wget \
    unzip \
    # X11 and GUI dependencies
    xvfb \
    x11vnc \
    fluxbox \
    xterm \
    # Qt5 runtime dependencies
    libqt5core5a \
    libqt5widgets5 \
    libqt5gui5 \
    libqt5network5 \
    libqt5webengine5 \
    libqt5webenginewidgets5 \
    libqt5webenginecore5 \
    # Additional Qt5 dependencies
    qt5-gtk-platformtheme \
    libqt5svg5 \
    libqt5multimedia5 \
    # OpenGL and graphics
    libgl1-mesa-glx \
    libgl1-mesa-dri \
    libglu1-mesa \
    mesa-utils \
    # Audio support (optional)
    pulseaudio \
    alsa-utils \
    # Fonts
    fonts-dejavu-core \
    fonts-liberation \
    # Web server for future web-based GUI
    nginx \
    # Process management
    supervisor \
    # Development tools for web client
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# Create app user (non-root for security)
RUN useradd -m -s /bin/bash -u 1000 aibox && \
    usermod -aG audio,video aibox

# Create app directory
WORKDIR /app

# Copy the ARM64 client binary
COPY build-container/bin/client_app /app/client_app

# Copy configuration files
COPY .env /app/.env

# Copy GUI setup scripts
COPY scripts/gui/ /app/scripts/gui/
COPY scripts/utils/ /app/scripts/utils/

# Create web client directory for future development
RUN mkdir -p /app/web-client

# Create placeholder web client
RUN echo '<!DOCTYPE html>' > /app/web-client/index.html && \
    echo '<html lang="en">' >> /app/web-client/index.html && \
    echo '<head>' >> /app/web-client/index.html && \
    echo '    <meta charset="UTF-8">' >> /app/web-client/index.html && \
    echo '    <meta name="viewport" content="width=device-width, initial-scale=1.0">' >> /app/web-client/index.html && \
    echo '    <title>C-AIBOX Client GUI</title>' >> /app/web-client/index.html && \
    echo '    <style>' >> /app/web-client/index.html && \
    echo '        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }' >> /app/web-client/index.html && \
    echo '        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }' >> /app/web-client/index.html && \
    echo '        h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }' >> /app/web-client/index.html && \
    echo '        .status { padding: 15px; background: #e7f3ff; border-left: 4px solid #007acc; margin: 20px 0; }' >> /app/web-client/index.html && \
    echo '        .info { background: #f0f8f0; border-left: 4px solid #28a745; }' >> /app/web-client/index.html && \
    echo '    </style>' >> /app/web-client/index.html && \
    echo '</head>' >> /app/web-client/index.html && \
    echo '<body>' >> /app/web-client/index.html && \
    echo '    <div class="container">' >> /app/web-client/index.html && \
    echo '        <h1>C-AIBOX Client GUI</h1>' >> /app/web-client/index.html && \
    echo '        <div class="status">' >> /app/web-client/index.html && \
    echo '            <strong>Status:</strong> Placeholder for future web-based client development' >> /app/web-client/index.html && \
    echo '        </div>' >> /app/web-client/index.html && \
    echo '        <div class="info">' >> /app/web-client/index.html && \
    echo '            <p><strong>Current Mode:</strong> Qt5 Desktop Application</p>' >> /app/web-client/index.html && \
    echo '            <p><strong>Future:</strong> Web-based GUI interface</p>' >> /app/web-client/index.html && \
    echo '            <p><strong>Access:</strong> VNC available on port 5900</p>' >> /app/web-client/index.html && \
    echo '        </div>' >> /app/web-client/index.html && \
    echo '    </div>' >> /app/web-client/index.html && \
    echo '</body>' >> /app/web-client/index.html && \
    echo '</html>' >> /app/web-client/index.html

# Make executables
RUN chmod +x /app/client_app && \
    chmod +x /app/scripts/gui/*.sh && \
    chmod +x /app/scripts/utils/*.sh

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/models /app/web-client && \
    chown -R aibox:aibox /app

# Create X11 directories
RUN mkdir -p /tmp/.X11-unix && \
    chmod 1777 /tmp/.X11-unix

# Set up VNC and X11 environment
ENV DISPLAY=:0
ENV VNC_PORT=5900
ENV VNC_RESOLUTION=1920x1080
ENV VNC_DEPTH=24

# Qt5 environment variables for container
ENV QT_X11_NO_MITSHM=1
ENV QT_QPA_PLATFORM=xcb
ENV QTWEBENGINE_DISABLE_SANDBOX=1
ENV LIBGL_ALWAYS_INDIRECT=1
ENV QT_LOGGING_RULES="qt.qpa.xcb.warning=false"
ENV QT_AUTO_SCREEN_SCALE_FACTOR=0
ENV QT_SCALE_FACTOR=1
ENV QT_FONT_DPI=96

# Create supervisor configuration
RUN mkdir -p /etc/supervisor/conf.d
COPY docker/supervisor-client.conf /etc/supervisor/conf.d/client.conf

# Configure nginx for web client (optional)
RUN echo 'server {' > /etc/nginx/sites-available/webclient && \
    echo '    listen 8081;' >> /etc/nginx/sites-available/webclient && \
    echo '    server_name localhost;' >> /etc/nginx/sites-available/webclient && \
    echo '    root /app/web-client;' >> /etc/nginx/sites-available/webclient && \
    echo '    index index.html;' >> /etc/nginx/sites-available/webclient && \
    echo '    location / {' >> /etc/nginx/sites-available/webclient && \
    echo '        try_files $uri $uri/ =404;' >> /etc/nginx/sites-available/webclient && \
    echo '    }' >> /etc/nginx/sites-available/webclient && \
    echo '}' >> /etc/nginx/sites-available/webclient && \
    ln -s /etc/nginx/sites-available/webclient /etc/nginx/sites-enabled/ && \
    rm -f /etc/nginx/sites-enabled/default

# Create startup script
COPY docker/start-client-gui.sh /app/start-client-gui.sh
RUN chmod +x /app/start-client-gui.sh

# Switch to app user
USER aibox

# Expose VNC and web client ports
EXPOSE 5900 8081

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD pgrep -f client_app || exit 1

# Start supervisor to manage X11, VNC, and client app
CMD ["/app/start-client-gui.sh"]
