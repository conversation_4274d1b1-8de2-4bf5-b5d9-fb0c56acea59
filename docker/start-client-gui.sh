#!/bin/bash

# C-AIBOX Client GUI Startup Script
# Manages X11, VNC, and Qt5 client application in container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Load environment variables from .env file
if [[ -f "/app/.env" ]]; then
    log_info "Loading environment configuration..."
    source /app/.env
else
    log_warning "No .env file found, using defaults"
fi

# Set default values if not provided
VNC_RESOLUTION=${VNC_GEOMETRY:-${DISPLAY_WIDTH:-1920}x${DISPLAY_HEIGHT:-1080}}
VNC_DEPTH=${VNC_DEPTH:-${DISPLAY_DEPTH:-24}}
VNC_PASSWORD=${VNC_PASSWORD:-"c-aibox123"}
DISPLAY_MODE=${DISPLAY_MODE:-"gui"}
CLIENT_TYPE=${CLIENT_TYPE:-"qt5"}
WEB_CLIENT_ENABLED=${WEB_CLIENT_ENABLED:-"false"}

log_info "Starting C-AIBOX Client GUI Container"
log_info "Client Type: $CLIENT_TYPE"
log_info "Display Mode: $DISPLAY_MODE"
log_info "VNC Resolution: $VNC_RESOLUTION"
log_info "VNC Depth: $VNC_DEPTH"
log_info "Web Client Enabled: $WEB_CLIENT_ENABLED"

# Create log directories
mkdir -p /var/log/supervisor
mkdir -p /app/logs

# Set up VNC password if provided
if [[ -n "$VNC_PASSWORD" && "$VNC_ENABLED" == "true" ]]; then
    log_info "Setting up VNC password..."
    mkdir -p ~/.vnc
    echo "$VNC_PASSWORD" | vncpasswd -f > ~/.vnc/passwd
    chmod 600 ~/.vnc/passwd
fi

# Check if running in headless mode
if [[ "$DISPLAY_MODE" == "headless" || "$TEST_HEADLESS" == "true" ]]; then
    log_info "Running in headless mode"
    export QT_QPA_PLATFORM=offscreen
    export QTWEBENGINE_CHROMIUM_FLAGS="--no-sandbox --disable-dev-shm-usage --disable-gpu"
    
    # Run client app directly in headless mode
    log_info "Starting client application in headless mode..."
    exec /app/client_app --headless
else
    log_info "Running in GUI mode with X11 and VNC"

    # Start nginx for web client if enabled
    if [[ "$WEB_CLIENT_ENABLED" == "true" ]]; then
        log_info "Starting nginx for web client..."
        nginx -t && nginx
    fi

    # Update supervisor configuration with environment variables
    envsubst < /etc/supervisor/conf.d/client.conf > /tmp/client.conf
    mv /tmp/client.conf /etc/supervisor/conf.d/client.conf

    # Start supervisor to manage all services
    log_info "Starting supervisor to manage GUI services..."
    exec /usr/bin/supervisord -c /etc/supervisor/supervisord.conf
fi
