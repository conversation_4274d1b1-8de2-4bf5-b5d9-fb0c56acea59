# Dockerfile for ARM64 Orange Pi deployment
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libstdc++6 \
    libc6 \
    libgcc-s1 \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy the ARM64 server binary
COPY build-container/bin/server /app/server

# Copy configuration files
COPY .env /app/.env

# Make server executable
RUN chmod +x /app/server

# Create models directory
RUN mkdir -p /app/models

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the server
CMD ["./server"]
