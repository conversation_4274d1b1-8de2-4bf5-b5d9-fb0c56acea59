FROM node:18-alpine

WORKDIR /app

# Install basic dependencies
RUN apk add --no-cache curl bash

# Create a simple mock server
RUN echo '{ "name": "c-aibox-mock-server", "version": "1.0.0", "dependencies": { "express": "^4.18.2" } }' > package.json
RUN npm install

# Create mock server
RUN echo 'const express = require("express");' > server.js && \
    echo 'const app = express();' >> server.js && \
    echo 'const port = process.env.SERVER_PORT || 8080;' >> server.js && \
    echo '' >> server.js && \
    echo 'app.use(express.json());' >> server.js && \
    echo '' >> server.js && \
    echo '// Health check endpoint' >> server.js && \
    echo 'app.get("/health", (req, res) => {' >> server.js && \
    echo '  res.json({ status: "ok", service: "c-aibox-mock-server" });' >> server.js && \
    echo '});' >> server.js && \
    echo '' >> server.js && \
    echo '// Mock API endpoints' >> server.js && \
    echo 'app.get("/api/status", (req, res) => {' >> server.js && \
    echo '  res.json({ status: "running", message: "Mock C-AIBOX Server" });' >> server.js && \
    echo '});' >> server.js && \
    echo '' >> server.js && \
    echo 'app.get("/api/models", (req, res) => {' >> server.js && \
    echo '  res.json({ models: ["mock-model-1", "mock-model-2"] });' >> server.js && \
    echo '});' >> server.js && \
    echo '' >> server.js && \
    echo 'app.post("/api/inference", (req, res) => {' >> server.js && \
    echo '  res.json({ result: "Mock inference result", timestamp: new Date().toISOString() });' >> server.js && \
    echo '});' >> server.js && \
    echo '' >> server.js && \
    echo 'app.listen(port, "0.0.0.0", () => {' >> server.js && \
    echo '  console.log(`Mock C-AIBOX Server running on port ${port}`);' >> server.js && \
    echo '});' >> server.js

# Create user
RUN addgroup -g 1001 aibox && \
    adduser -D -s /bin/bash -u 1001 -G aibox aibox

# Create directories
RUN mkdir -p /app/data /app/logs /app/models && \
    chown -R aibox:aibox /app

USER aibox

EXPOSE 8080

CMD ["node", "server.js"]
