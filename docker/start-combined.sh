#!/bin/bash

# C-AIBOX Combined Server + Client Startup Script
# Manages both API server and GUI client in single container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Load environment variables from .env file
if [[ -f "/app/.env" ]]; then
    log_info "Loading environment configuration..."
    source /app/.env
else
    log_warning "No .env file found, using defaults"
fi

# Set default values if not provided
VNC_RESOLUTION=${VNC_GEOMETRY:-${DISPLAY_WIDTH:-1920}x${DISPLAY_HEIGHT:-1080}}
VNC_DEPTH=${VNC_DEPTH:-${DISPLAY_DEPTH:-24}}
VNC_PASSWORD=${VNC_PASSWORD:-"c-aibox123"}
DISPLAY_MODE=${DISPLAY_MODE:-"gui"}
SERVER_HOST=${SERVER_HOST:-"0.0.0.0"}
SERVER_PORT=${SERVER_PORT:-"8080"}

log_info "Starting C-AIBOX Combined Services Container"
log_info "Server: $SERVER_HOST:$SERVER_PORT"
log_info "Display Mode: $DISPLAY_MODE"
log_info "VNC Resolution: $VNC_RESOLUTION"

# Create log directories
mkdir -p /var/log/supervisor
mkdir -p /app/logs

# Set up VNC password if provided
if [[ -n "$VNC_PASSWORD" && "$VNC_ENABLED" == "true" ]]; then
    log_info "Setting up VNC password..."
    mkdir -p ~/.vnc
    echo "$VNC_PASSWORD" | vncpasswd -f > ~/.vnc/passwd
    chmod 600 ~/.vnc/passwd
fi

# Export environment variables for supervisor
export VNC_RESOLUTION
export VNC_DEPTH
export SERVER_HOST
export SERVER_PORT

# Check if running in headless mode
if [[ "$DISPLAY_MODE" == "headless" || "$TEST_HEADLESS" == "true" ]]; then
    log_info "Running in headless mode"
    
    # Start server in background
    log_info "Starting server..."
    /app/server --port "$SERVER_PORT" --host "$SERVER_HOST" &
    SERVER_PID=$!
    
    # Start client in headless mode
    log_info "Starting client in headless mode..."
    export QT_QPA_PLATFORM=offscreen
    export QTWEBENGINE_CHROMIUM_FLAGS="--no-sandbox --disable-dev-shm-usage --disable-gpu"
    /app/client_app --headless &
    CLIENT_PID=$!
    
    # Wait for both processes
    wait $SERVER_PID $CLIENT_PID
else
    log_info "Running in GUI mode with X11 and VNC"
    
    # Update supervisor configuration with environment variables
    envsubst < /etc/supervisor/conf.d/combined.conf > /tmp/combined.conf
    mv /tmp/combined.conf /etc/supervisor/conf.d/combined.conf
    
    # Start supervisor to manage all services
    log_info "Starting supervisor to manage combined services..."
    exec /usr/bin/supervisord -c /etc/supervisor/supervisord.conf
fi
