# Dockerfile for C-AIBOX Web-based Client GUI
# Optimized for Orange Pi ARM64 deployment with modern web technologies

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    nginx \
    supervisor \
    curl \
    bash

# Create app user (use different UID/GID to avoid conflicts)
RUN addgroup -g 1001 aibox && \
    adduser -D -s /bin/bash -u 1001 -G aibox aibox

# Create necessary directories
RUN mkdir -p /app/web-client /app/logs /var/log/supervisor /var/log/nginx

# Copy package.json for future web client development
RUN echo '{ "name": "c-aibox-web-client", "version": "1.0.0", "description": "C-AIBOX Web Client", "main": "index.js", "scripts": { "start": "node server.js", "build": "echo Building web client...", "dev": "echo Development mode..." }, "dependencies": { "express": "^4.18.0", "socket.io": "^4.7.0" }, "devDependencies": { "nodemon": "^3.0.0" } }' > /app/package.json

# Install Node.js dependencies (placeholder)
RUN npm install

# Create placeholder web client
RUN echo '<!DOCTYPE html>' > /app/web-client/index.html && \
    echo '<html lang="en">' >> /app/web-client/index.html && \
    echo '<head>' >> /app/web-client/index.html && \
    echo '    <meta charset="UTF-8">' >> /app/web-client/index.html && \
    echo '    <meta name="viewport" content="width=device-width, initial-scale=1.0">' >> /app/web-client/index.html && \
    echo '    <title>C-AIBOX Web Client</title>' >> /app/web-client/index.html && \
    echo '    <style>' >> /app/web-client/index.html && \
    echo '        * { margin: 0; padding: 0; box-sizing: border-box; }' >> /app/web-client/index.html && \
    echo '        body { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }' >> /app/web-client/index.html && \
    echo '        .container { background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); max-width: 600px; width: 90%; text-align: center; }' >> /app/web-client/index.html && \
    echo '        h1 { color: #333; margin-bottom: 20px; font-size: 2.5em; }' >> /app/web-client/index.html && \
    echo '        .status { padding: 20px; background: #e3f2fd; border-radius: 8px; margin: 20px 0; border-left: 5px solid #2196f3; }' >> /app/web-client/index.html && \
    echo '        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }' >> /app/web-client/index.html && \
    echo '        .feature { background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; }' >> /app/web-client/index.html && \
    echo '        .feature h3 { color: #495057; margin-bottom: 8px; }' >> /app/web-client/index.html && \
    echo '        .feature p { color: #6c757d; font-size: 0.9em; }' >> /app/web-client/index.html && \
    echo '        .btn { display: inline-block; padding: 12px 24px; background: #007acc; color: white; text-decoration: none; border-radius: 6px; margin: 10px; transition: background 0.3s; }' >> /app/web-client/index.html && \
    echo '        .btn:hover { background: #005a9e; }' >> /app/web-client/index.html && \
    echo '    </style>' >> /app/web-client/index.html && \
    echo '</head>' >> /app/web-client/index.html && \
    echo '<body>' >> /app/web-client/index.html && \
    echo '    <div class="container">' >> /app/web-client/index.html && \
    echo '        <h1>🤖 C-AIBOX</h1>' >> /app/web-client/index.html && \
    echo '        <div class="status">' >> /app/web-client/index.html && \
    echo '            <strong>Web Client Ready for Development</strong>' >> /app/web-client/index.html && \
    echo '        </div>' >> /app/web-client/index.html && \
    echo '        <div class="features">' >> /app/web-client/index.html && \
    echo '            <div class="feature">' >> /app/web-client/index.html && \
    echo '                <h3>🎥 Camera UI</h3>' >> /app/web-client/index.html && \
    echo '                <p>Real-time camera feed with custom marks and activities</p>' >> /app/web-client/index.html && \
    echo '            </div>' >> /app/web-client/index.html && \
    echo '            <div class="feature">' >> /app/web-client/index.html && \
    echo '                <h3>🧠 AI Models</h3>' >> /app/web-client/index.html && \
    echo '                <p>Face recognition and object detection controls</p>' >> /app/web-client/index.html && \
    echo '            </div>' >> /app/web-client/index.html && \
    echo '            <div class="feature">' >> /app/web-client/index.html && \
    echo '                <h3>⚙️ Settings</h3>' >> /app/web-client/index.html && \
    echo '                <p>Configuration and system management</p>' >> /app/web-client/index.html && \
    echo '            </div>' >> /app/web-client/index.html && \
    echo '        </div>' >> /app/web-client/index.html && \
    echo '        <a href="#" class="btn">Start Development</a>' >> /app/web-client/index.html && \
    echo '        <a href="http://localhost:5900" class="btn">VNC Access</a>' >> /app/web-client/index.html && \
    echo '    </div>' >> /app/web-client/index.html && \
    echo '</body>' >> /app/web-client/index.html && \
    echo '</html>' >> /app/web-client/index.html

# Create placeholder Node.js server
RUN echo 'const express = require("express");' > /app/server.js && \
    echo 'const path = require("path");' >> /app/server.js && \
    echo 'const app = express();' >> /app/server.js && \
    echo 'const PORT = process.env.WEB_CLIENT_PORT || 3000;' >> /app/server.js && \
    echo '' >> /app/server.js && \
    echo 'app.use(express.static(path.join(__dirname, "web-client")));' >> /app/server.js && \
    echo '' >> /app/server.js && \
    echo 'app.get("/", (req, res) => {' >> /app/server.js && \
    echo '    res.sendFile(path.join(__dirname, "web-client", "index.html"));' >> /app/server.js && \
    echo '});' >> /app/server.js && \
    echo '' >> /app/server.js && \
    echo 'app.get("/api/status", (req, res) => {' >> /app/server.js && \
    echo '    res.json({ status: "ready", mode: "development", timestamp: new Date().toISOString() });' >> /app/server.js && \
    echo '});' >> /app/server.js && \
    echo '' >> /app/server.js && \
    echo 'app.listen(PORT, "0.0.0.0", () => {' >> /app/server.js && \
    echo '    console.log(`C-AIBOX Web Client running on port ${PORT}`);' >> /app/server.js && \
    echo '});' >> /app/server.js

# Configure nginx
RUN echo 'server {' > /etc/nginx/http.d/default.conf && \
    echo '    listen 80;' >> /etc/nginx/http.d/default.conf && \
    echo '    server_name localhost;' >> /etc/nginx/http.d/default.conf && \
    echo '    location / {' >> /etc/nginx/http.d/default.conf && \
    echo '        proxy_pass http://localhost:3000;' >> /etc/nginx/http.d/default.conf && \
    echo '        proxy_http_version 1.1;' >> /etc/nginx/http.d/default.conf && \
    echo '        proxy_set_header Upgrade $http_upgrade;' >> /etc/nginx/http.d/default.conf && \
    echo '        proxy_set_header Connection "upgrade";' >> /etc/nginx/http.d/default.conf && \
    echo '        proxy_set_header Host $host;' >> /etc/nginx/http.d/default.conf && \
    echo '        proxy_cache_bypass $http_upgrade;' >> /etc/nginx/http.d/default.conf && \
    echo '    }' >> /etc/nginx/http.d/default.conf && \
    echo '}' >> /etc/nginx/http.d/default.conf

# Create supervisor configuration directory and file
RUN mkdir -p /etc/supervisor/conf.d && \
    echo '[supervisord]' > /etc/supervisor/conf.d/web-client.conf && \
    echo 'nodaemon=true' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'user=root' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'logfile=/var/log/supervisor/supervisord.log' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'pidfile=/var/run/supervisord.pid' >> /etc/supervisor/conf.d/web-client.conf && \
    echo '' >> /etc/supervisor/conf.d/web-client.conf && \
    echo '[program:nginx]' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'command=/usr/sbin/nginx -g "daemon off;"' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'priority=100' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'stdout_logfile=/var/log/supervisor/nginx.log' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'stderr_logfile=/var/log/supervisor/nginx.log' >> /etc/supervisor/conf.d/web-client.conf && \
    echo '' >> /etc/supervisor/conf.d/web-client.conf && \
    echo '[program:web-client]' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'command=/usr/local/bin/node /app/server.js' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'directory=/app' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'user=aibox' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'priority=200' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'stdout_logfile=/var/log/supervisor/web-client.log' >> /etc/supervisor/conf.d/web-client.conf && \
    echo 'stderr_logfile=/var/log/supervisor/web-client.log' >> /etc/supervisor/conf.d/web-client.conf

# Set ownership
RUN chown -R aibox:aibox /app

# Expose ports
EXPOSE 80 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:3000/api/status || exit 1

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisord.conf"]
