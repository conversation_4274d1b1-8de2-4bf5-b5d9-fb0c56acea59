# Combined Dockerfile for C-AIBOX Server + Client GUI
# Optimized for Orange Pi ARM64 deployment

FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install system dependencies for both server and GUI
RUN apt-get update && apt-get install -y \
    # Basic runtime dependencies
    libstdc++6 \
    libc6 \
    libgcc-s1 \
    ca-certificates \
    curl \
    # X11 and GUI dependencies
    xvfb \
    x11vnc \
    fluxbox \
    xterm \
    # Qt5 runtime dependencies
    libqt5core5a \
    libqt5widgets5 \
    libqt5gui5 \
    libqt5network5 \
    libqt5webengine5 \
    libqt5webenginewidgets5 \
    libqt5webenginecore5 \
    # Additional Qt5 dependencies
    qt5-gtk-platformtheme \
    libqt5svg5 \
    libqt5multimedia5 \
    # OpenGL and graphics
    libgl1-mesa-glx \
    libgl1-mesa-dri \
    libglu1-mesa \
    mesa-utils \
    # Audio support (optional)
    pulseaudio \
    alsa-utils \
    # Fonts
    fonts-dejavu-core \
    fonts-liberation \
    # Process management
    supervisor \
    # Networking tools
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# Create app user (non-root for security)
RUN useradd -m -s /bin/bash -u 1000 aibox && \
    usermod -aG audio,video aibox

# Create app directory
WORKDIR /app

# Copy the ARM64 binaries
COPY build-container/bin/server /app/server
COPY build-container/bin/client_app /app/client_app

# Copy configuration files
COPY .env /app/.env

# Copy scripts
COPY scripts/gui/ /app/scripts/gui/
COPY scripts/utils/ /app/scripts/utils/

# Make executables
RUN chmod +x /app/server /app/client_app && \
    chmod +x /app/scripts/gui/*.sh && \
    chmod +x /app/scripts/utils/*.sh

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/models && \
    chown -R aibox:aibox /app

# Create X11 directories
RUN mkdir -p /tmp/.X11-unix && \
    chmod 1777 /tmp/.X11-unix

# Set up environment variables
ENV DISPLAY=:0
ENV VNC_PORT=5900
ENV VNC_RESOLUTION=1920x1080
ENV VNC_DEPTH=24

# Qt5 environment variables
ENV QT_X11_NO_MITSHM=1
ENV QT_QPA_PLATFORM=xcb
ENV QTWEBENGINE_DISABLE_SANDBOX=1
ENV LIBGL_ALWAYS_INDIRECT=1
ENV QT_LOGGING_RULES="qt.qpa.xcb.warning=false"

# Create supervisor configuration for combined services
RUN mkdir -p /etc/supervisor/conf.d
COPY docker/supervisor-combined.conf /etc/supervisor/conf.d/combined.conf

# Create startup script
COPY docker/start-combined.sh /app/start-combined.sh
RUN chmod +x /app/start-combined.sh

# Switch to app user for application processes
# (supervisor will run as root but spawn processes as aibox user)

# Expose ports
EXPOSE 8080 5900

# Health check for both services
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD curl -f http://localhost:8080/health && pgrep -f client_app || exit 1

# Start combined services
CMD ["/app/start-combined.sh"]
