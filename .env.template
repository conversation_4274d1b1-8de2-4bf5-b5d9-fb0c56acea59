# C-AIBOX Deployment Configuration Template
# Copy this file to .env and customize for your deployment

# =============================================================================
# DEPLOYMENT SETTINGS
# =============================================================================

# Orange Pi Target Configuration
ORANGE_PI_IP=***************
ORANGE_PI_USER=orangepi
ORANGE_PI_PORT=22
ORANGE_PI_SSH_KEY=

# Deployment Directories
DEPLOY_DIR=/home/<USER>/c-aibox
BUILD_DIR=build-orangepi
MODELS_DIR=/home/<USER>/c-aibox/models

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker Images
SERVER_IMAGE_NAME=c-aibox-server
CLIENT_IMAGE_NAME=c-aibox-client
CLIENT_WEB_IMAGE_NAME=c-aibox-web-client
COMBINED_IMAGE_NAME=c-aibox-combined
IMAGE_TAG=latest

# Container Names
SERVER_CONTAINER_NAME=c-aibox-server
CLIENT_CONTAINER_NAME=c-aibox-client
CLIENT_WEB_CONTAINER_NAME=c-aibox-web-client
COMBINED_CONTAINER_NAME=c-aibox-combined

# Port Mappings
SERVER_HOST_PORT=8080
SERVER_CONTAINER_PORT=8080
CLIENT_VNC_PORT=5900
CLIENT_WEB_PORT=3000
CLIENT_WEB_NGINX_PORT=8081
CLIENT_DISPLAY_PORT=6000

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# API Server Settings
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_TIMEOUT=30
SERVER_DEBUG=false

# CORS Settings
CORS_ENABLED=true
CORS_ORIGIN=*
CORS_METHODS=GET, POST, PUT, DELETE, OPTIONS
CORS_HEADERS=Content-Type, Authorization

# AI Model Settings
MODELS_BASE_PATH=./models
MAX_CONCURRENT_REQUESTS=10
INFERENCE_TIMEOUT_MS=30000

# Performance Settings
ENABLE_THREADING=true
THREAD_POOL_SIZE=4

# =============================================================================
# CLIENT GUI CONFIGURATION
# =============================================================================

# Client Type Selection (qt5, web, both)
CLIENT_TYPE=qt5

# Display Settings
DISPLAY_MODE=gui
DISPLAY_WIDTH=1920
DISPLAY_HEIGHT=1080
DISPLAY_DEPTH=24

# X11 Settings
X11_DISPLAY=:0
X11_XAUTH_FILE=/tmp/.X11-unix/X0
X11_SOCKET_DIR=/tmp/.X11-unix

# Qt5 Settings
QT_QPA_PLATFORM=xcb
QT_X11_NO_MITSHM=1
QTWEBENGINE_DISABLE_SANDBOX=1
LIBGL_ALWAYS_INDIRECT=1
QT_LOGGING_RULES=qt.qpa.xcb.warning=false

# VNC Settings (for remote access)
VNC_ENABLED=true
VNC_PASSWORD=c-aibox123
VNC_GEOMETRY=1920x1080
VNC_DEPTH=24

# Web Client Settings
WEB_CLIENT_ENABLED=true
WEB_CLIENT_PORT=3000
WEB_CLIENT_HOST=0.0.0.0
WEB_CLIENT_NGINX_PORT=8081
WEB_CLIENT_DEV_MODE=true

# =============================================================================
# CAMERA CONFIGURATION
# =============================================================================

# RTSP Camera Settings
CAMERA_ENABLED=true
CAMERA_RTSP_URL=rtsp://admin:password@*************:554/stream
CAMERA_WIDTH=1920
CAMERA_HEIGHT=1080
CAMERA_FPS=30

# Face Recognition Settings
FACE_DETECTION_ENABLED=true
FACE_RECOGNITION_ENABLED=true
FACE_CONFIDENCE_THRESHOLD=0.8
FACE_DATABASE_PATH=./data/faces.db

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Settings
LOG_LEVEL=info
LOG_FILE_PATH=logs/c-aibox.log
LOG_MAX_FILE_SIZE=10MB
LOG_MAX_FILES=5
LOG_CONSOLE_OUTPUT=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# API Security
API_KEY=your_api_key_here
JWT_SECRET=your_jwt_secret_here
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development Mode
DEV_MODE=false
DEV_AUTO_RELOAD=false
DEV_MOCK_CAMERA=false
DEV_MOCK_AI_MODELS=false

# Testing Settings
TEST_MODE=false
TEST_HEADLESS=false
TEST_TIMEOUT=30

# =============================================================================
# DEPLOYMENT PROFILES
# =============================================================================

# Docker Compose Profiles:
# - combined: Single container with server + Qt5 client
# - separate: Separate containers for server and Qt5 client
# - web-client: Web-based client only
# - separate-web: Separate containers for server and web client

# Example deployment commands:
# ./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile combined
# ./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile separate --client-type qt5
# ./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile separate --client-type web
# ./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile separate --client-type both
# ./scripts/deploy/deploy-client-gui.sh --ip *************** --type qt5
# ./scripts/deploy/deploy-client-gui.sh --ip *************** --type web
# ./scripts/deploy/deploy-client-gui.sh --ip *************** --type both
