#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QListWidget>
#include <QTableWidget>
#include <QProgressBar>
#include <QSlider>
#include <QTabWidget>
#include <QTextEdit>
#include <QTimer>
#include <QHeaderView>

/**
 * @brief Control panel for managing RTSP streams and system settings
 * 
 * This widget provides controls for adding/removing streams, configuring
 * display settings, and monitoring system performance.
 */
class StreamControlPanel : public QWidget
{
    Q_OBJECT

public:
    explicit StreamControlPanel(QWidget *parent = nullptr);
    ~StreamControlPanel();

    // Stream management
    void addStreamToList(int streamId, const QString& url, const QString& status);
    void removeStreamFromList(int streamId);
    void updateStreamStatus(int streamId, const QString& status);
    void updateStreamStats(int streamId, const QString& stats);

    // Settings
    void setMaxStreamsPerRow(int count);
    void setShowFaceOverlays(bool show);
    void setShowStatistics(bool show);
    void setAutoReconnect(bool enable);

signals:
    void addStreamRequested(const QString& url, const QString& username, const QString& password);
    void removeStreamRequested(int streamId);
    void startAllStreamsRequested();
    void stopAllStreamsRequested();
    void streamSelectionChanged(int streamId);
    void settingsChanged();
    void maxStreamsPerRowChanged(int count);
    void showFaceOverlaysChanged(bool show);
    void showStatisticsChanged(bool show);
    void autoReconnectChanged(bool enable);

private slots:
    void onAddStreamClicked();
    void onRemoveStreamClicked();
    void onStartAllClicked();
    void onStopAllClicked();
    void onStreamListSelectionChanged();
    void onSettingsChanged();
    void onRefreshStreams();
    void onExportLogs();
    void onClearLogs();

private:
    void setupUI();
    void setupStreamManagement();
    void setupDisplaySettings();
    void setupSystemMonitoring();
    void setupLogging();
    void connectSignals();
    void updateStreamList();
    void addLogEntry(const QString& message, const QString& level = "INFO");

    // Main layout
    QVBoxLayout* m_mainLayout;
    QTabWidget* m_tabWidget;

    // Stream Management Tab
    QWidget* m_streamTab;
    QGroupBox* m_addStreamGroup;
    QLineEdit* m_urlLineEdit;
    QLineEdit* m_usernameLineEdit;
    QLineEdit* m_passwordLineEdit;
    QPushButton* m_addButton;
    
    QGroupBox* m_streamListGroup;
    QTableWidget* m_streamTable;
    QPushButton* m_removeButton;
    QPushButton* m_startAllButton;
    QPushButton* m_stopAllButton;
    QPushButton* m_refreshButton;

    // Display Settings Tab
    QWidget* m_settingsTab;
    QGroupBox* m_displayGroup;
    QSpinBox* m_maxStreamsSpinBox;
    QCheckBox* m_showOverlaysCheckBox;
    QCheckBox* m_showStatsCheckBox;
    QCheckBox* m_autoReconnectCheckBox;
    
    QGroupBox* m_qualityGroup;
    QComboBox* m_resolutionComboBox;
    QSlider* m_qualitySlider;
    QLabel* m_qualityLabel;
    
    QGroupBox* m_performanceGroup;
    QSpinBox* m_bufferSizeSpinBox;
    QSpinBox* m_timeoutSpinBox;
    QSpinBox* m_retryCountSpinBox;

    // System Monitoring Tab
    QWidget* m_monitoringTab;
    QGroupBox* m_systemStatsGroup;
    QLabel* m_cpuLabel;
    QProgressBar* m_cpuProgressBar;
    QLabel* m_memoryLabel;
    QProgressBar* m_memoryProgressBar;
    QLabel* m_networkLabel;
    QProgressBar* m_networkProgressBar;
    
    QGroupBox* m_streamStatsGroup;
    QLabel* m_totalStreamsLabel;
    QLabel* m_activeStreamsLabel;
    QLabel* m_totalFpsLabel;
    QLabel* m_totalBitrateLabel;

    // Logging Tab
    QWidget* m_loggingTab;
    QTextEdit* m_logTextEdit;
    QHBoxLayout* m_logButtonLayout;
    QPushButton* m_exportLogsButton;
    QPushButton* m_clearLogsButton;
    QComboBox* m_logLevelComboBox;

    // Data
    QMap<int, QString> m_streamUrls;
    QMap<int, QString> m_streamStatuses;
    
    // Timers
    QTimer* m_updateTimer;
};
