#pragma once

#include <QWidget>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFrame>
#include <QPushButton>
#include <QProgressBar>
#include <QTimer>
#include <QPixmap>
#include <QPainter>
#include <QRect>
#include <QColor>
#include <QFont>
#include <QMouseEvent>
#include <QMenu>
#include <QAction>
#include <QDateTime>
#include <memory>

/**
 * @brief Face detection result for overlay display
 */
struct FaceDetection {
    QRect boundingBox;
    float confidence;
    QString personId;
    QString personName;
    QString department;
    bool isAuthorized;
    QColor overlayColor;
    
    FaceDetection() : confidence(0.0f), isAuthorized(false), overlayColor(Qt::green) {}
};

/**
 * @brief Stream statistics for display
 */
struct StreamStats {
    QString streamUrl;
    QString status;
    int fps;
    int bitrate;
    int frameCount;
    int droppedFrames;
    float cpuUsage;
    float memoryUsage;
    QDateTime lastUpdate;
    
    StreamStats() : fps(0), bitrate(0), frameCount(0), droppedFrames(0), 
                   cpuUsage(0.0f), memoryUsage(0.0f) {}
};

/**
 * @brief Widget for displaying individual RTSP stream with face recognition overlays
 * 
 * This widget displays a mock video stream with simulated face detection and
 * recognition results. It includes controls for stream management and statistics.
 */
class RTSPStreamWidget : public QFrame
{
    Q_OBJECT

public:
    explicit RTSPStreamWidget(int streamId, const QString& streamUrl, QWidget *parent = nullptr);
    ~RTSPStreamWidget();

    // Stream control
    void startStream();
    void stopStream();
    void restartStream();
    bool isStreaming() const { return m_isStreaming; }
    
    // Stream properties
    int getStreamId() const { return m_streamId; }
    QString getStreamUrl() const { return m_streamUrl; }
    void setStreamUrl(const QString& url);
    
    // Display settings
    void setShowOverlays(bool show);
    void setShowStatistics(bool show);
    void setStreamTitle(const QString& title);
    
    // Mock data for testing
    void addMockFaceDetection(const FaceDetection& face);
    void clearFaceDetections();
    void updateMockFrame();

signals:
    void streamClicked(int streamId);
    void streamDoubleClicked(int streamId);
    void streamContextMenu(int streamId, const QPoint& position);
    void streamStatusChanged(int streamId, const QString& status);

protected:
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseDoubleClickEvent(QMouseEvent* event) override;
    void contextMenuEvent(QContextMenuEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;

private slots:
    void onUpdateFrame();
    void onUpdateStats();
    void onToggleStream();
    void onShowStreamInfo();

private:
    void setupUI();
    void setupContextMenu();
    void drawVideoFrame(QPainter& painter);
    void drawFaceOverlays(QPainter& painter);
    void drawStreamInfo(QPainter& painter);
    void drawStatistics(QPainter& painter);
    void generateMockFrame();
    void generateMockFaces();
    void updateStreamStats();

    // Stream properties
    int m_streamId;
    QString m_streamUrl;
    QString m_streamTitle;
    bool m_isStreaming;
    bool m_isSelected;
    
    // Display settings
    bool m_showOverlays;
    bool m_showStatistics;
    bool m_showStreamInfo;
    
    // UI components
    QVBoxLayout* m_layout;
    QLabel* m_titleLabel;
    QLabel* m_statusLabel;
    QPushButton* m_toggleButton;
    QProgressBar* m_bufferBar;
    
    // Video display
    QPixmap m_currentFrame;
    QRect m_videoRect;
    
    // Face detection data
    QList<FaceDetection> m_faceDetections;
    
    // Statistics
    StreamStats m_stats;
    
    // Timers
    QTimer* m_frameUpdateTimer;
    QTimer* m_statsUpdateTimer;
    
    // Context menu
    QMenu* m_contextMenu;
    QAction* m_startAction;
    QAction* m_stopAction;
    QAction* m_restartAction;
    QAction* m_infoAction;
    QAction* m_removeAction;
    
    // Mock data generation
    int m_mockFrameCounter;
    QColor m_mockBackgroundColor;
};
