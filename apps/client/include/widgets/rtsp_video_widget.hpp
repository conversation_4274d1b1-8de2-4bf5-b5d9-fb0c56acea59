#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QFrame>
#include <QTimer>
// Note: Qt Multimedia not available, using mock video display
#include <QPainter>
#include <QMouseEvent>
#include <QContextMenuEvent>
#include <QMenu>
#include <QAction>
#include <QDateTime>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMutex>

/**
 * @brief Face detection result for overlay display
 */
struct FaceDetectionResult {
    QRect boundingBox;
    float confidence;
    QString personId;
    QString personName;
    QString department;
    bool isAuthorized;
    QColor overlayColor;
    QDateTime timestamp;
    
    FaceDetectionResult() : confidence(0.0f), isAuthorized(false), overlayColor(Qt::green) {}
};

/**
 * @brief Stream statistics
 */
struct StreamStatistics {
    QString streamUrl;
    QString status;
    int fps;
    int bitrate;
    int frameCount;
    int droppedFrames;
    float cpuUsage;
    float memoryUsage;
    QDateTime lastUpdate;
    bool isPlaying;

    StreamStatistics() : fps(0), bitrate(0), frameCount(0), droppedFrames(0),
                        cpuUsage(0.0f), memoryUsage(0.0f), isPlaying(false) {}
};

/**
 * @brief Real RTSP Video Widget with Face Recognition Overlay
 * 
 * This widget displays real RTSP streams using Qt Multimedia and overlays
 * face detection results received from the server via WebSocket.
 */
class RTSPVideoWidget : public QFrame
{
    Q_OBJECT

public:
    explicit RTSPVideoWidget(int streamId, const QString& streamUrl, const QString& streamName, QWidget *parent = nullptr);
    ~RTSPVideoWidget();

    // Stream control
    void startStream();
    void stopStream();
    void restartStream();
    bool isStreaming() const;
    
    // Stream properties
    int getStreamId() const { return m_streamId; }
    QString getStreamUrl() const { return m_streamUrl; }
    QString getStreamName() const { return m_streamName; }
    void setStreamUrl(const QString& url);
    void setStreamName(const QString& name);
    
    // Display settings
    void setShowOverlays(bool show);
    void setShowStatistics(bool show);
    void setShowStreamInfo(bool show);
    
    // Face detection results
    void addFaceDetectionResult(const FaceDetectionResult& result);
    void clearFaceDetectionResults();
    
    // Statistics
    StreamStatistics getStatistics() const { return m_stats; }

signals:
    void streamClicked(int streamId);
    void streamDoubleClicked(int streamId);
    void streamContextMenu(int streamId, const QPoint& position);
    void streamStatusChanged(int streamId, const QString& status);
    void streamError(int streamId, const QString& error);

protected:
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseDoubleClickEvent(QMouseEvent* event) override;
    void contextMenuEvent(QContextMenuEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;

private slots:
    void onUpdateStats();
    void onToggleStream();
    void onShowStreamInfo();
    void onReconnectStream();
    void onUpdateMockFrame();

private:
    void setupUI();
    void setupMockVideo();
    void setupContextMenu();
    void drawFaceOverlays(QPainter& painter);
    void drawStreamInfo(QPainter& painter);
    void drawStatistics(QPainter& painter);
    void updateStreamStats();
    void cleanupOldFaceResults();
    void generateMockFrame();
    void scheduleReconnect();

    // Stream properties
    int m_streamId;
    QString m_streamUrl;
    QString m_streamName;
    bool m_isSelected;
    
    // Display settings
    bool m_showOverlays;
    bool m_showStatistics;
    bool m_showStreamInfo;
    
    // UI components
    QVBoxLayout* m_layout;
    QLabel* m_titleLabel;
    QLabel* m_statusLabel;
    QPushButton* m_toggleButton;
    QLabel* m_videoDisplayLabel;  // Mock video display

    // Mock video data
    QPixmap m_currentFrame;
    int m_mockFrameCounter;
    
    // Face detection data
    QList<FaceDetectionResult> m_faceResults;
    QMutex m_faceResultsMutex;
    
    // Statistics
    StreamStatistics m_stats;
    
    // Timers
    QTimer* m_statsUpdateTimer;
    QTimer* m_reconnectTimer;
    QTimer* m_cleanupTimer;
    
    // Context menu
    QMenu* m_contextMenu;
    QAction* m_startAction;
    QAction* m_stopAction;
    QAction* m_restartAction;
    QAction* m_infoAction;
    QAction* m_reconnectAction;
    
    // Network
    QNetworkAccessManager* m_networkManager;
    
    // State
    bool m_isReconnecting;
    bool m_isStreaming;
    int m_reconnectAttempts;
    static const int MAX_RECONNECT_ATTEMPTS = 5;
    static const int RECONNECT_DELAY_MS = 3000;
    static const int FACE_RESULT_TIMEOUT_MS = 5000;
};
