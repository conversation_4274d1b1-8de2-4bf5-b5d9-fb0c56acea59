#pragma once

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTabWidget>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QSpinBox>
#include <QCheckBox>
#include <QComboBox>
#include <QPushButton>
#include <QDialogButtonBox>
#include <QListWidget>
#include <QTableWidget>
#include <QInputDialog>
#include <QMessageBox>

/**
 * @brief Configuration Dialog for C-AIBOX Client
 * 
 * This dialog allows users to configure:
 * - Server connection settings
 * - Stream display settings
 * - Add/remove RTSP streams
 * - Application preferences
 */
class ConfigurationDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ConfigurationDialog(QWidget *parent = nullptr);
    ~ConfigurationDialog();

    // Server settings
    void setServerAddress(const QString& address);
    QString getServerAddress() const;
    void setServerPort(int port);
    int getServerPort() const;
    void setAutoConnect(bool autoConnect);
    bool getAutoConnect() const;

    // Display settings
    void setMaxStreamsPerRow(int count);
    int getMaxStreamsPerRow() const;
    void setShowOverlays(bool show);
    bool getShowOverlays() const;

    // Stream management
    void showAddStreamDialog();
    void addStreamToList(const QString& url, const QString& name);
    void removeSelectedStream();

signals:
    void configurationChanged();
    void streamAddRequested(const QString& url, const QString& name);
    void streamRemoveRequested(const QString& url);

protected:
    void accept() override;
    void reject() override;

private slots:
    void onAddStreamClicked();
    void onRemoveStreamClicked();
    void onEditStreamClicked();
    void onTestConnectionClicked();
    void onRestoreDefaultsClicked();
    void onStreamSelectionChanged();

private:
    void setupUI();
    void setupServerTab();
    void setupDisplayTab();
    void setupStreamsTab();
    void setupAdvancedTab();
    void loadSettings();
    void saveSettings();
    void validateSettings();
    void connectSignals();
    void updateServiceUrls();
    void updateStreamsTable();

    // Main layout
    QVBoxLayout* m_mainLayout;
    QTabWidget* m_tabWidget;
    QDialogButtonBox* m_buttonBox;

    // Server Tab
    QWidget* m_serverTab;
    QLineEdit* m_serverAddressEdit;
    QSpinBox* m_serverPortSpinBox;
    QCheckBox* m_autoConnectCheckBox;
    QLineEdit* m_websocketUrlEdit;
    QLineEdit* m_apiUrlEdit;
    QPushButton* m_testConnectionButton;

    // Display Tab
    QWidget* m_displayTab;
    QSpinBox* m_maxStreamsPerRowSpinBox;
    QCheckBox* m_showOverlaysCheckBox;
    QCheckBox* m_showStatisticsCheckBox;
    QCheckBox* m_showStreamInfoCheckBox;
    QComboBox* m_overlayStyleComboBox;
    QSpinBox* m_updateIntervalSpinBox;

    // Streams Tab
    QWidget* m_streamsTab;
    QTableWidget* m_streamsTable;
    QPushButton* m_addStreamButton;
    QPushButton* m_removeStreamButton;
    QPushButton* m_editStreamButton;

    // Advanced Tab
    QWidget* m_advancedTab;
    QSpinBox* m_bufferSizeSpinBox;
    QCheckBox* m_dropFramesCheckBox;
    QSpinBox* m_maxCpuUsageSpinBox;
    QCheckBox* m_enableGpuAccelCheckBox;
    QSpinBox* m_reconnectIntervalSpinBox;
    QSpinBox* m_maxReconnectAttemptsSpinBox;
    QPushButton* m_restoreDefaultsButton;

    // Stream data
    struct StreamInfo {
        QString url;
        QString name;
        QString username;
        QString password;
        bool enabled;
    };
    QList<StreamInfo> m_streamList;
};
