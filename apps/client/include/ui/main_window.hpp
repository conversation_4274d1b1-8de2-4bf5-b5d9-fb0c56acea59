#pragma once

#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QFrame>
#include <QScrollArea>
#include <QTimer>
#include <QSplitter>
#include <QTabWidget>
#include <QGroupBox>
#include <QProgressBar>
#include <QListWidget>
#include <QTableWidget>
#include <QSpinBox>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <memory>

// Forward declarations
class RTSPStreamWidget;
class RTSPManagerWidget;
class StreamControlPanel;
class SystemStatsWidget;

/**
 * @brief Main window for C-AIBOX RTSP Stream Viewer with Face Recognition
 * 
 * This is a skeleton implementation that displays mock RTSP streams with
 * face detection and recognition overlays. It can work without actual
 * RTSP server implementation.
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onAddStreamClicked();
    void onRemoveStreamClicked();
    void onStartAllStreamsClicked();
    void onStopAllStreamsClicked();
    void onStreamSelectionChanged();
    void onUpdateStats();
    void onToggleFullscreen();
    void onShowSettings();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupCentralWidget();
    void setupStreamArea();
    void setupControlPanel();
    void setupStatsPanel();
    void loadStyleSheet();
    void connectSignals();
    void startMockDataTimer();

    // UI Components
    QWidget* m_centralWidget;
    QSplitter* m_mainSplitter;
    QSplitter* m_rightSplitter;
    
    // Stream area
    QWidget* m_streamAreaWidget;
    QScrollArea* m_streamScrollArea;
    QGridLayout* m_streamGridLayout;
    
    // Control panels
    StreamControlPanel* m_controlPanel;
    QWidget* m_statsWidget;
    
    // Tab widget for different views
    QTabWidget* m_tabWidget;
    QWidget* m_streamTab;
    QWidget* m_settingsTab;
    QWidget* m_logsTab;
    
    // Stream management
    QList<RTSPStreamWidget*> m_streamWidgets;
    int m_nextStreamId;
    
    // Timers
    QTimer* m_statsUpdateTimer;
    QTimer* m_mockDataTimer;
    
    // Status bar elements
    QLabel* m_statusLabel;
    QLabel* m_connectionCountLabel;
    QLabel* m_fpsLabel;
    QProgressBar* m_cpuUsageBar;
    QProgressBar* m_memoryUsageBar;
    
    // Settings
    int m_maxStreamsPerRow;
    bool m_showFaceOverlays;
    bool m_showStatistics;
    bool m_autoReconnect;
};
