#include <QApplication>
#include <QMainWindow>
#include <QStatusBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QFrame>
#include <QTimer>
#include <QPixmap>
#include <QPainter>
#include <QDateTime>
#include <QRandomGenerator>
#include <QtMath>
#include <QDebug>

/**
 * @brief Simple RTSP Stream Viewer Demo
 * 
 * This is a simplified version without complex Qt MOC dependencies
 * that demonstrates the basic UI layout and mock streaming functionality.
 */
class SimpleStreamWidget : public QFrame
{
public:
    explicit SimpleStreamWidget(int streamId, const QString& streamUrl, QWidget *parent = nullptr)
        : QFrame(parent), m_streamId(streamId), m_streamUrl(streamUrl), m_frameCounter(0)
    {
        setMinimumSize(320, 240);
        setFrameStyle(QFrame::Box | QFrame::Raised);
        setLineWidth(2);
        
        // Create layout
        QVBoxLayout* layout = new QVBoxLayout(this);
        
        // Title
        QLabel* titleLabel = new QLabel(QString("Stream %1").arg(streamId));
        titleLabel->setAlignment(Qt::AlignCenter);
        titleLabel->setStyleSheet("font-weight: bold; color: #4da8da; padding: 5px;");
        layout->addWidget(titleLabel);
        
        // Video area (will be painted)
        layout->addStretch();
        
        // Controls
        QHBoxLayout* controlsLayout = new QHBoxLayout();
        
        QLabel* statusLabel = new QLabel("Streaming");
        statusLabel->setStyleSheet("color: green; font-weight: bold;");
        controlsLayout->addWidget(statusLabel);
        
        controlsLayout->addStretch();
        
        QPushButton* infoBtn = new QPushButton("Info");
        infoBtn->setMaximumWidth(50);
        controlsLayout->addWidget(infoBtn);
        
        layout->addLayout(controlsLayout);
        
        // Start animation timer
        QTimer* timer = new QTimer(this);
        connect(timer, &QTimer::timeout, this, [this]() {
            m_frameCounter++;
            update();
        });
        timer->start(33); // ~30 FPS
    }

protected:
    void paintEvent(QPaintEvent* event) override
    {
        QFrame::paintEvent(event);
        
        QPainter painter(this);
        painter.setRenderHint(QPainter::Antialiasing);
        
        // Calculate video area
        QRect videoRect = rect().adjusted(10, 30, -10, -50);
        
        // Draw mock video background
        painter.fillRect(videoRect, QColor(30, 42, 68));
        
        // Draw animated elements
        int time = m_frameCounter;
        
        // Moving circle
        int circleX = (time * 2) % videoRect.width();
        int circleY = videoRect.height() / 2 + 50 * qSin(time * 0.1);
        painter.setBrush(QColor(100, 150, 255, 100));
        painter.setPen(Qt::NoPen);
        painter.drawEllipse(videoRect.left() + circleX - 15, videoRect.top() + circleY - 15, 30, 30);
        
        // Grid pattern
        painter.setPen(QPen(QColor(60, 80, 120), 1));
        for (int x = 0; x < videoRect.width(); x += 40) {
            painter.drawLine(videoRect.left() + x, videoRect.top(), 
                           videoRect.left() + x, videoRect.bottom());
        }
        for (int y = 0; y < videoRect.height(); y += 40) {
            painter.drawLine(videoRect.left(), videoRect.top() + y, 
                           videoRect.right(), videoRect.top() + y);
        }
        
        // Draw timestamp
        painter.setPen(Qt::white);
        painter.setFont(QFont("Arial", 10));
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
        painter.drawText(videoRect.adjusted(10, 10, -10, -10), Qt::AlignTop | Qt::AlignLeft, timestamp);
        
        // Draw stream info
        QString streamInfo = QString("Stream %1\n%2x%3 @ 30 FPS")
                            .arg(m_streamId)
                            .arg(videoRect.width())
                            .arg(videoRect.height());
        painter.drawText(videoRect.adjusted(10, 10, -10, -10), Qt::AlignTop | Qt::AlignRight, streamInfo);
        
        // Draw mock face detection boxes
        if (time % 120 < 60) { // Show faces for half the time
            painter.setPen(QPen(Qt::green, 2));
            painter.setBrush(Qt::NoBrush);
            
            // Mock face 1
            QRect face1(videoRect.left() + 50, videoRect.top() + 40, 80, 100);
            painter.drawRect(face1);
            painter.drawText(face1.adjusted(0, -20, 0, 0), Qt::AlignBottom | Qt::AlignLeft, "John Doe (95%)");
            
            // Mock face 2
            painter.setPen(QPen(Qt::red, 2));
            QRect face2(videoRect.right() - 130, videoRect.top() + 60, 80, 100);
            painter.drawRect(face2);
            painter.drawText(face2.adjusted(0, -20, 0, 0), Qt::AlignBottom | Qt::AlignLeft, "Unknown (78%)");
        }
        
        // Draw statistics
        painter.setPen(Qt::yellow);
        painter.setFont(QFont("Arial", 8));
        QString stats = QString("Bitrate: %1 kbps\nFrames: %2\nFPS: 30")
                       .arg(2000 + (time % 500))
                       .arg(time);
        painter.drawText(videoRect.adjusted(10, 10, -10, -10), Qt::AlignBottom | Qt::AlignLeft, stats);
    }

private:
    int m_streamId;
    QString m_streamUrl;
    int m_frameCounter;
};

class SimpleMainWindow : public QMainWindow
{
public:
    explicit SimpleMainWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setWindowTitle("C-AIBOX - RTSP Stream Viewer (Demo)");
        setMinimumSize(1200, 800);
        
        // Create central widget
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        // Create main layout
        QHBoxLayout* mainLayout = new QHBoxLayout(centralWidget);
        
        // Create stream area
        QWidget* streamArea = new QWidget();
        QGridLayout* streamGrid = new QGridLayout(streamArea);
        
        // Add demo streams
        for (int i = 1; i <= 4; ++i) {
            SimpleStreamWidget* streamWidget = new SimpleStreamWidget(i, QString("rtsp://demo.camera.com/stream%1").arg(i));
            int row = (i - 1) / 2;
            int col = (i - 1) % 2;
            streamGrid->addWidget(streamWidget, row, col);
        }
        
        mainLayout->addWidget(streamArea, 3);
        
        // Create control panel
        QWidget* controlPanel = new QWidget();
        controlPanel->setMaximumWidth(300);
        controlPanel->setStyleSheet("background-color: #f5f5f5; border: 1px solid #ddd;");
        
        QVBoxLayout* controlLayout = new QVBoxLayout(controlPanel);
        
        // Title
        QLabel* titleLabel = new QLabel("Stream Control");
        titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; color: #4da8da; padding: 10px;");
        controlLayout->addWidget(titleLabel);
        
        // Add stream button
        QPushButton* addBtn = new QPushButton("Add Stream");
        addBtn->setStyleSheet("QPushButton { background-color: #4da8da; color: white; border: none; border-radius: 5px; padding: 8px; font-weight: bold; }");
        controlLayout->addWidget(addBtn);
        
        // Stream list
        QLabel* listLabel = new QLabel("Active Streams:");
        listLabel->setStyleSheet("font-weight: bold; margin-top: 20px;");
        controlLayout->addWidget(listLabel);
        
        for (int i = 1; i <= 4; ++i) {
            QLabel* streamLabel = new QLabel(QString("Stream %1: Streaming").arg(i));
            streamLabel->setStyleSheet("color: green; padding: 2px;");
            controlLayout->addWidget(streamLabel);
        }
        
        // System stats
        QLabel* statsLabel = new QLabel("System Statistics:");
        statsLabel->setStyleSheet("font-weight: bold; margin-top: 20px;");
        controlLayout->addWidget(statsLabel);
        
        QLabel* cpuLabel = new QLabel("CPU Usage: 25%");
        QLabel* memLabel = new QLabel("Memory Usage: 45%");
        QLabel* fpsLabel = new QLabel("Total FPS: 120");
        
        controlLayout->addWidget(cpuLabel);
        controlLayout->addWidget(memLabel);
        controlLayout->addWidget(fpsLabel);
        
        controlLayout->addStretch();
        
        mainLayout->addWidget(controlPanel, 1);
        
        // Status bar
        statusBar()->showMessage("C-AIBOX RTSP Stream Viewer - Demo Mode - 4 streams active");
        
        qDebug() << "Simple RTSP Stream Viewer Demo started successfully";
    }
};

int main(int argc, char* argv[])
{
    QApplication app(argc, argv);
    
    app.setApplicationName("C-AIBOX RTSP Demo");
    app.setApplicationVersion("1.0.0");
    
    SimpleMainWindow window;
    window.show();
    
    return app.exec();
}
