#include <QApplication>
#include <QCoreApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QWidget>
#include <QLabel>
#include <QDebug>
#include <QTimer>
#include <utils/env_utils.hpp>
#include "ui/main_window.hpp"

/**
 * @brief Main entry point for the Simplified Web Client Application
 * @param argc Command line argument count
 * @param argv Command line arguments
 * @return Application exit code
 */
int main(int argc, char* argv[]) {
    // Check if we should run in headless mode (for testing/CI)
    bool headless = false;
    for (int i = 1; i < argc; ++i) {
        if (std::string(argv[i]) == "--headless") {
            headless = true;
            break;
        }
    }

    // Configure Qt platform and WebEngine BEFORE QApplication
    if (headless || !qgetenv("DISPLAY").size()) {
        qputenv("QT_QPA_PLATFORM", "offscreen");
        // Disable all OpenGL/hardware acceleration for headless mode
        qputenv("QT_OPENGL", "software");
        qputenv("LIBGL_ALWAYS_SOFTWARE", "1");
        qputenv("QTWEBENGINE_CHROMIUM_FLAGS", "--no-sandbox --disable-dev-shm-usage --disable-gpu --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding");
        qDebug() << "Running in headless mode";
    } else {
        // Try to use xcb for GUI mode, fallback to offscreen if it fails
        qputenv("QT_QPA_PLATFORM", "xcb");

        // OpenGL configuration for container environments
        qputenv("LIBGL_ALWAYS_INDIRECT", "1");
        qputenv("LIBGL_ALWAYS_SOFTWARE", "1");
        qputenv("QT_OPENGL", "software");
        qputenv("QT_QUICK_BACKEND", "software");

        // Mesa configuration for software rendering
        qputenv("MESA_GL_VERSION_OVERRIDE", "3.3");
        qputenv("MESA_GLSL_VERSION_OVERRIDE", "330");
        qputenv("GALLIUM_DRIVER", "llvmpipe");
        qputenv("LP_NUM_THREADS", "1");

        // WebEngine configuration with software rendering
        qputenv("QTWEBENGINE_CHROMIUM_FLAGS", "--no-sandbox --disable-dev-shm-usage --disable-gpu --disable-software-rasterizer --disable-gpu-sandbox --disable-accelerated-2d-canvas --disable-accelerated-jpeg-decoding --disable-accelerated-mjpeg-decode --disable-accelerated-video-decode --disable-accelerated-video-encode");

        // Suppress OpenGL warnings for container environment
        qputenv("QT_LOGGING_RULES", "qt.qpa.xcb.warning=false;qt.qpa.gl.warning=false;qt.qpa.xcb.glx.warning=false");

        qDebug() << "GUI mode configured for container environment (software rendering)";
    }

    // WebEngine configuration for container environments
    qputenv("QTWEBENGINE_DISABLE_SANDBOX", "1");

    // Set Qt attributes BEFORE creating QApplication
    if (headless) {
        // Headless mode attributes
        QApplication::setAttribute(Qt::AA_EnableHighDpiScaling, false);
        QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps, false);
        QApplication::setAttribute(Qt::AA_DisableHighDpiScaling, true);
    } else {
        // GUI mode attributes
        QApplication::setAttribute(Qt::AA_EnableHighDpiScaling, true);
        QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps, true);
    }
    QApplication::setAttribute(Qt::AA_DisableWindowContextHelpButton, true);

    // Configure OpenGL for container environment
    if (headless) {
        // Disable OpenGL completely for headless mode
        QApplication::setAttribute(Qt::AA_UseSoftwareOpenGL, true);
        QApplication::setAttribute(Qt::AA_UseDesktopOpenGL, false);
        QApplication::setAttribute(Qt::AA_UseOpenGLES, false);
    } else {
        // Use software OpenGL for GUI mode
        QApplication::setAttribute(Qt::AA_UseSoftwareOpenGL, true);
        QApplication::setAttribute(Qt::AA_UseDesktopOpenGL, false);
        QApplication::setAttribute(Qt::AA_UseOpenGLES, false);
        // Additional attributes for better container compatibility
        QApplication::setAttribute(Qt::AA_ShareOpenGLContexts, false);
    }

    // Load environment variables from .env file
    utils::loadConfig(".env");

    // Create application instance based on mode
    QCoreApplication* app = nullptr;
    if (headless) {
        // Use QCoreApplication for headless mode to avoid GUI/OpenGL issues
        app = new QCoreApplication(argc, argv);
        qDebug() << "Created QCoreApplication for headless mode";
    } else {
        // Try to create QApplication for GUI mode with error handling
        bool gui_failed = false;

        try {
            app = new QApplication(argc, argv);
            qDebug() << "Created QApplication for GUI mode";

            // Test if X11 connection actually works
            QApplication* guiApp = qobject_cast<QApplication*>(app);
            if (guiApp && guiApp->platformName().contains("xcb")) {
                qDebug() << "XCB platform initialized successfully";
            } else if (guiApp && guiApp->platformName().contains("offscreen")) {
                qDebug() << "Running in offscreen mode";
                headless = true; // Treat offscreen as headless
            } else if (guiApp) {
                qDebug() << "GUI platform:" << guiApp->platformName();
            }
        } catch (const std::exception& e) {
            qWarning() << "Failed to create QApplication:" << e.what();
            gui_failed = true;
        }

        // If GUI failed, try with offscreen platform
        if (gui_failed) {
            qWarning() << "GUI mode failed, trying offscreen mode...";
            if (app) delete app;

            // Force offscreen platform
            qputenv("QT_QPA_PLATFORM", "offscreen");

            try {
                app = new QApplication(argc, argv);
                qDebug() << "Created QApplication in offscreen mode";
                headless = true; // Treat as headless
            } catch (const std::exception& e2) {
                qWarning() << "Offscreen mode also failed, falling back to QCoreApplication";
                if (app) delete app;
                app = new QCoreApplication(argc, argv);
                headless = true;
            }
        }
    }

    // Set application properties
    app->setApplicationName("C-AIBOX Web Client");
    app->setApplicationVersion("1.0.0");
    app->setOrganizationName("C-AIBOX");

    // Check if GUI is actually available after QApplication creation
    QApplication* guiApp = qobject_cast<QApplication*>(app);
    if (!headless && guiApp) {
        // Try to detect if X11 is actually working
        bool x11_available = false;
        if (qgetenv("DISPLAY").size() > 0) {
            // We have DISPLAY set, assume X11 might work
            x11_available = true;
            qDebug() << "X11 display detected:" << qgetenv("DISPLAY");
        }

        if (!x11_available) {
            qWarning() << "X11 not available, GUI features may be limited";
        }
    }

    // Create GUI components only if not in headless mode
    QWidget* window = nullptr;
    if (!headless && guiApp) {
        try {
            window = new MainWindow();
            qDebug() << "C-AIBOX RTSP Stream Viewer started successfully";
        } catch (const std::exception& e) {
            qWarning() << "Failed to create main window:" << e.what();

            // Fallback: create a simple window
            QMainWindow* fallbackWindow = new QMainWindow();
            fallbackWindow->setWindowTitle("C-AIBOX - Error");
            fallbackWindow->setMinimumSize(800, 600);

            QWidget* centralWidget = new QWidget(fallbackWindow);
            QVBoxLayout* layout = new QVBoxLayout(centralWidget);

            QLabel* errorLabel = new QLabel("Failed to initialize RTSP Stream Viewer\n\nError: " + QString(e.what()), centralWidget);
            errorLabel->setAlignment(Qt::AlignCenter);
            errorLabel->setStyleSheet("font-size: 16px; color: #aa0000; padding: 50px;");
            layout->addWidget(errorLabel);

            fallbackWindow->setCentralWidget(centralWidget);
            window = fallbackWindow;

            qWarning() << "C-AIBOX started in fallback mode due to initialization error";
        }
    }

    // Handle different modes
    if (headless) {
        // Headless mode - just run for a few seconds and exit
        qDebug() << "Running in headless mode - simulating application run";
        QTimer::singleShot(3000, app, &QCoreApplication::quit);
        qDebug() << "Headless mode timer set for 3 seconds";
    } else if (guiApp && window) {
        // GUI mode - show window
        window->show();
        qDebug() << "GUI window displayed";
    }

    // Run application
    int result = app->exec();

    // Cleanup
    delete app;
    return result;
}
