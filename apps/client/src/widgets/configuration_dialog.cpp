#include "widgets/configuration_dialog.hpp"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTabWidget>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QSpinBox>
#include <QCheckBox>
#include <QComboBox>
#include <QPushButton>
#include <QDialogButtonBox>
#include <QTableWidget>
#include <QHeaderView>
#include <QInputDialog>
#include <QMessageBox>
#include <QSettings>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QTimer>
#include <QDebug>

ConfigurationDialog::ConfigurationDialog(QWidget *parent)
    : QDialog(parent)
    , m_mainLayout(nullptr)
    , m_tabWidget(nullptr)
    , m_buttonBox(nullptr)
    , m_serverTab(nullptr)
    , m_serverAddressEdit(nullptr)
    , m_serverPortSpinBox(nullptr)
    , m_autoConnectCheckBox(nullptr)
    , m_websocketUrlEdit(nullptr)
    , m_apiUrlEdit(nullptr)
    , m_testConnectionButton(nullptr)
    , m_displayTab(nullptr)
    , m_maxStreamsPerRowSpinBox(nullptr)
    , m_showOverlaysCheckBox(nullptr)
    , m_showStatisticsCheckBox(nullptr)
    , m_showStreamInfoCheckBox(nullptr)
    , m_overlayStyleComboBox(nullptr)
    , m_updateIntervalSpinBox(nullptr)
    , m_streamsTab(nullptr)
    , m_streamsTable(nullptr)
    , m_addStreamButton(nullptr)
    , m_removeStreamButton(nullptr)
    , m_editStreamButton(nullptr)
    , m_advancedTab(nullptr)
    , m_bufferSizeSpinBox(nullptr)
    , m_dropFramesCheckBox(nullptr)
    , m_maxCpuUsageSpinBox(nullptr)
    , m_enableGpuAccelCheckBox(nullptr)
    , m_reconnectIntervalSpinBox(nullptr)
    , m_maxReconnectAttemptsSpinBox(nullptr)
    , m_restoreDefaultsButton(nullptr)
{
    setWindowTitle("C-AIBOX Configuration");
    setMinimumSize(600, 500);
    resize(700, 600);
    setModal(true);

    setupUI();
    connectSignals();
    loadSettings();
}

ConfigurationDialog::~ConfigurationDialog()
{
}

void ConfigurationDialog::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    
    // Create tab widget
    m_tabWidget = new QTabWidget();
    m_mainLayout->addWidget(m_tabWidget);
    
    // Setup tabs
    setupServerTab();
    setupDisplayTab();
    setupStreamsTab();
    setupAdvancedTab();
    
    // Add tabs to widget
    m_tabWidget->addTab(m_serverTab, "Server");
    m_tabWidget->addTab(m_displayTab, "Display");
    m_tabWidget->addTab(m_streamsTab, "Streams");
    m_tabWidget->addTab(m_advancedTab, "Advanced");
    
    // Dialog buttons
    m_buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel | QDialogButtonBox::Apply);
    m_mainLayout->addWidget(m_buttonBox);
    
    connect(m_buttonBox, &QDialogButtonBox::accepted, this, &ConfigurationDialog::accept);
    connect(m_buttonBox, &QDialogButtonBox::rejected, this, &ConfigurationDialog::reject);
    connect(m_buttonBox->button(QDialogButtonBox::Apply), &QPushButton::clicked, this, [this]() {
        saveSettings();
        emit configurationChanged();
    });
}

void ConfigurationDialog::setupServerTab()
{
    m_serverTab = new QWidget();
    QVBoxLayout* serverLayout = new QVBoxLayout(m_serverTab);
    
    // Connection settings group
    QGroupBox* connectionGroup = new QGroupBox("Server Connection");
    QGridLayout* connectionLayout = new QGridLayout(connectionGroup);
    
    connectionLayout->addWidget(new QLabel("Server Address:"), 0, 0);
    m_serverAddressEdit = new QLineEdit();
    m_serverAddressEdit->setPlaceholderText("*************");
    connectionLayout->addWidget(m_serverAddressEdit, 0, 1);
    
    connectionLayout->addWidget(new QLabel("Server Port:"), 1, 0);
    m_serverPortSpinBox = new QSpinBox();
    m_serverPortSpinBox->setRange(1, 65535);
    m_serverPortSpinBox->setValue(8080);
    connectionLayout->addWidget(m_serverPortSpinBox, 1, 1);
    
    m_autoConnectCheckBox = new QCheckBox("Auto-connect on startup");
    connectionLayout->addWidget(m_autoConnectCheckBox, 2, 0, 1, 2);
    
    serverLayout->addWidget(connectionGroup);
    
    // URL settings group
    QGroupBox* urlGroup = new QGroupBox("Service URLs");
    QGridLayout* urlLayout = new QGridLayout(urlGroup);
    
    urlLayout->addWidget(new QLabel("WebSocket URL:"), 0, 0);
    m_websocketUrlEdit = new QLineEdit();
    m_websocketUrlEdit->setPlaceholderText("ws://*************:8080");
    m_websocketUrlEdit->setReadOnly(true);
    urlLayout->addWidget(m_websocketUrlEdit, 0, 1);
    
    urlLayout->addWidget(new QLabel("API URL:"), 1, 0);
    m_apiUrlEdit = new QLineEdit();
    m_apiUrlEdit->setPlaceholderText("http://*************:8081");
    m_apiUrlEdit->setReadOnly(true);
    urlLayout->addWidget(m_apiUrlEdit, 1, 1);
    
    serverLayout->addWidget(urlGroup);
    
    // Test connection
    m_testConnectionButton = new QPushButton("Test Connection");
    serverLayout->addWidget(m_testConnectionButton);
    
    serverLayout->addStretch();
}

void ConfigurationDialog::setupDisplayTab()
{
    m_displayTab = new QWidget();
    QVBoxLayout* displayLayout = new QVBoxLayout(m_displayTab);
    
    // Layout settings group
    QGroupBox* layoutGroup = new QGroupBox("Layout Settings");
    QGridLayout* layoutGridLayout = new QGridLayout(layoutGroup);
    
    layoutGridLayout->addWidget(new QLabel("Max Streams per Row:"), 0, 0);
    m_maxStreamsPerRowSpinBox = new QSpinBox();
    m_maxStreamsPerRowSpinBox->setRange(1, 6);
    m_maxStreamsPerRowSpinBox->setValue(2);
    layoutGridLayout->addWidget(m_maxStreamsPerRowSpinBox, 0, 1);
    
    displayLayout->addWidget(layoutGroup);
    
    // Overlay settings group
    QGroupBox* overlayGroup = new QGroupBox("Overlay Settings");
    QGridLayout* overlayGridLayout = new QGridLayout(overlayGroup);
    
    m_showOverlaysCheckBox = new QCheckBox("Show Face Detection Overlays");
    m_showOverlaysCheckBox->setChecked(true);
    overlayGridLayout->addWidget(m_showOverlaysCheckBox, 0, 0, 1, 2);
    
    m_showStatisticsCheckBox = new QCheckBox("Show Stream Statistics");
    m_showStatisticsCheckBox->setChecked(true);
    overlayGridLayout->addWidget(m_showStatisticsCheckBox, 1, 0, 1, 2);
    
    m_showStreamInfoCheckBox = new QCheckBox("Show Stream Information");
    m_showStreamInfoCheckBox->setChecked(true);
    overlayGridLayout->addWidget(m_showStreamInfoCheckBox, 2, 0, 1, 2);
    
    overlayGridLayout->addWidget(new QLabel("Overlay Style:"), 3, 0);
    m_overlayStyleComboBox = new QComboBox();
    m_overlayStyleComboBox->addItems({"Modern", "Classic", "Minimal"});
    overlayGridLayout->addWidget(m_overlayStyleComboBox, 3, 1);
    
    displayLayout->addWidget(overlayGroup);
    
    // Performance settings group
    QGroupBox* performanceGroup = new QGroupBox("Performance Settings");
    QGridLayout* performanceGridLayout = new QGridLayout(performanceGroup);
    
    performanceGridLayout->addWidget(new QLabel("Update Interval (ms):"), 0, 0);
    m_updateIntervalSpinBox = new QSpinBox();
    m_updateIntervalSpinBox->setRange(16, 1000);
    m_updateIntervalSpinBox->setValue(33);
    m_updateIntervalSpinBox->setSuffix(" ms");
    performanceGridLayout->addWidget(m_updateIntervalSpinBox, 0, 1);
    
    displayLayout->addWidget(performanceGroup);
    displayLayout->addStretch();
}

void ConfigurationDialog::setupStreamsTab()
{
    m_streamsTab = new QWidget();
    QVBoxLayout* streamsLayout = new QVBoxLayout(m_streamsTab);
    
    // Streams table
    m_streamsTable = new QTableWidget(0, 4);
    m_streamsTable->setHorizontalHeaderLabels({"Name", "URL", "Username", "Enabled"});
    m_streamsTable->horizontalHeader()->setStretchLastSection(true);
    m_streamsTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_streamsTable->setAlternatingRowColors(true);
    streamsLayout->addWidget(m_streamsTable);
    
    // Buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    
    m_addStreamButton = new QPushButton("Add Stream");
    m_addStreamButton->setIcon(style()->standardIcon(QStyle::SP_FileDialogNewFolder));
    buttonLayout->addWidget(m_addStreamButton);
    
    m_editStreamButton = new QPushButton("Edit Stream");
    m_editStreamButton->setIcon(style()->standardIcon(QStyle::SP_FileDialogDetailedView));
    m_editStreamButton->setEnabled(false);
    buttonLayout->addWidget(m_editStreamButton);
    
    m_removeStreamButton = new QPushButton("Remove Stream");
    m_removeStreamButton->setIcon(style()->standardIcon(QStyle::SP_TrashIcon));
    m_removeStreamButton->setEnabled(false);
    buttonLayout->addWidget(m_removeStreamButton);
    
    buttonLayout->addStretch();
    
    streamsLayout->addLayout(buttonLayout);
}

void ConfigurationDialog::setupAdvancedTab()
{
    m_advancedTab = new QWidget();
    QVBoxLayout* advancedLayout = new QVBoxLayout(m_advancedTab);
    
    // Buffer settings group
    QGroupBox* bufferGroup = new QGroupBox("Buffer Settings");
    QGridLayout* bufferGridLayout = new QGridLayout(bufferGroup);
    
    bufferGridLayout->addWidget(new QLabel("Buffer Size (ms):"), 0, 0);
    m_bufferSizeSpinBox = new QSpinBox();
    m_bufferSizeSpinBox->setRange(50, 5000);
    m_bufferSizeSpinBox->setValue(100);
    m_bufferSizeSpinBox->setSuffix(" ms");
    bufferGridLayout->addWidget(m_bufferSizeSpinBox, 0, 1);
    
    m_dropFramesCheckBox = new QCheckBox("Drop frames on delay");
    m_dropFramesCheckBox->setChecked(true);
    bufferGridLayout->addWidget(m_dropFramesCheckBox, 1, 0, 1, 2);
    
    advancedLayout->addWidget(bufferGroup);
    
    // Performance settings group
    QGroupBox* perfGroup = new QGroupBox("Performance Settings");
    QGridLayout* perfGridLayout = new QGridLayout(perfGroup);
    
    perfGridLayout->addWidget(new QLabel("Max CPU Usage (%):"), 0, 0);
    m_maxCpuUsageSpinBox = new QSpinBox();
    m_maxCpuUsageSpinBox->setRange(10, 100);
    m_maxCpuUsageSpinBox->setValue(80);
    m_maxCpuUsageSpinBox->setSuffix("%");
    perfGridLayout->addWidget(m_maxCpuUsageSpinBox, 0, 1);
    
    m_enableGpuAccelCheckBox = new QCheckBox("Enable GPU acceleration");
    m_enableGpuAccelCheckBox->setChecked(true);
    perfGridLayout->addWidget(m_enableGpuAccelCheckBox, 1, 0, 1, 2);
    
    advancedLayout->addWidget(perfGroup);
    
    // Connection settings group
    QGroupBox* connGroup = new QGroupBox("Connection Settings");
    QGridLayout* connGridLayout = new QGridLayout(connGroup);
    
    connGridLayout->addWidget(new QLabel("Reconnect Interval (ms):"), 0, 0);
    m_reconnectIntervalSpinBox = new QSpinBox();
    m_reconnectIntervalSpinBox->setRange(1000, 30000);
    m_reconnectIntervalSpinBox->setValue(3000);
    m_reconnectIntervalSpinBox->setSuffix(" ms");
    connGridLayout->addWidget(m_reconnectIntervalSpinBox, 0, 1);
    
    connGridLayout->addWidget(new QLabel("Max Reconnect Attempts:"), 1, 0);
    m_maxReconnectAttemptsSpinBox = new QSpinBox();
    m_maxReconnectAttemptsSpinBox->setRange(1, 20);
    m_maxReconnectAttemptsSpinBox->setValue(5);
    connGridLayout->addWidget(m_maxReconnectAttemptsSpinBox, 1, 1);
    
    advancedLayout->addWidget(connGroup);
    
    // Restore defaults button
    m_restoreDefaultsButton = new QPushButton("Restore Defaults");
    advancedLayout->addWidget(m_restoreDefaultsButton);
    
    advancedLayout->addStretch();
}

void ConfigurationDialog::connectSignals()
{
    // Server tab signals
    connect(m_serverAddressEdit, &QLineEdit::textChanged, this, [this]() {
        updateServiceUrls();
    });
    connect(m_serverPortSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, [this]() {
        updateServiceUrls();
    });
    connect(m_testConnectionButton, &QPushButton::clicked, this, &ConfigurationDialog::onTestConnectionClicked);

    // Streams tab signals
    connect(m_addStreamButton, &QPushButton::clicked, this, &ConfigurationDialog::onAddStreamClicked);
    connect(m_editStreamButton, &QPushButton::clicked, this, &ConfigurationDialog::onEditStreamClicked);
    connect(m_removeStreamButton, &QPushButton::clicked, this, &ConfigurationDialog::onRemoveStreamClicked);
    connect(m_streamsTable, &QTableWidget::itemSelectionChanged, this, &ConfigurationDialog::onStreamSelectionChanged);

    // Advanced tab signals
    connect(m_restoreDefaultsButton, &QPushButton::clicked, this, &ConfigurationDialog::onRestoreDefaultsClicked);
}

void ConfigurationDialog::updateServiceUrls()
{
    QString address = m_serverAddressEdit->text();
    int port = m_serverPortSpinBox->value();

    m_websocketUrlEdit->setText(QString("ws://%1:%2").arg(address).arg(port));
    m_apiUrlEdit->setText(QString("http://%1:%2").arg(address).arg(port + 1));
}

// Public getters and setters
void ConfigurationDialog::setServerAddress(const QString& address)
{
    m_serverAddressEdit->setText(address);
}

QString ConfigurationDialog::getServerAddress() const
{
    return m_serverAddressEdit->text();
}

void ConfigurationDialog::setServerPort(int port)
{
    m_serverPortSpinBox->setValue(port);
}

int ConfigurationDialog::getServerPort() const
{
    return m_serverPortSpinBox->value();
}

void ConfigurationDialog::setAutoConnect(bool autoConnect)
{
    m_autoConnectCheckBox->setChecked(autoConnect);
}

bool ConfigurationDialog::getAutoConnect() const
{
    return m_autoConnectCheckBox->isChecked();
}

void ConfigurationDialog::setMaxStreamsPerRow(int count)
{
    m_maxStreamsPerRowSpinBox->setValue(count);
}

int ConfigurationDialog::getMaxStreamsPerRow() const
{
    return m_maxStreamsPerRowSpinBox->value();
}

void ConfigurationDialog::setShowOverlays(bool show)
{
    m_showOverlaysCheckBox->setChecked(show);
}

bool ConfigurationDialog::getShowOverlays() const
{
    return m_showOverlaysCheckBox->isChecked();
}

void ConfigurationDialog::showAddStreamDialog()
{
    m_tabWidget->setCurrentWidget(m_streamsTab);
    show();
    onAddStreamClicked();
}

void ConfigurationDialog::addStreamToList(const QString& url, const QString& name)
{
    StreamInfo stream;
    stream.url = url;
    stream.name = name;
    stream.enabled = true;

    m_streamList.append(stream);
    updateStreamsTable();
}

void ConfigurationDialog::removeSelectedStream()
{
    int currentRow = m_streamsTable->currentRow();
    if (currentRow >= 0 && currentRow < m_streamList.size()) {
        m_streamList.removeAt(currentRow);
        updateStreamsTable();
    }
}

void ConfigurationDialog::accept()
{
    validateSettings();
    saveSettings();
    emit configurationChanged();
    QDialog::accept();
}

void ConfigurationDialog::reject()
{
    loadSettings(); // Restore original settings
    QDialog::reject();
}

// Private slots
void ConfigurationDialog::onAddStreamClicked()
{
    bool ok;
    QString name = QInputDialog::getText(this, "Add Stream", "Stream Name:", QLineEdit::Normal, "", &ok);
    if (!ok || name.isEmpty()) return;

    QString url = QInputDialog::getText(this, "Add Stream", "RTSP URL:", QLineEdit::Normal,
                                       "rtsp://admin:CMC2024!@192.168.222.169:554/streaming/channels/01", &ok);
    if (!ok || url.isEmpty()) return;

    QString username = QInputDialog::getText(this, "Add Stream", "Username (optional):", QLineEdit::Normal, "", &ok);
    if (!ok) return;

    QString password;
    if (!username.isEmpty()) {
        password = QInputDialog::getText(this, "Add Stream", "Password:", QLineEdit::Password, "", &ok);
        if (!ok) return;
    }

    StreamInfo stream;
    stream.name = name;
    stream.url = url;
    stream.username = username;
    stream.password = password;
    stream.enabled = true;

    m_streamList.append(stream);
    updateStreamsTable();

    emit streamAddRequested(url, name);
}

void ConfigurationDialog::onRemoveStreamClicked()
{
    int currentRow = m_streamsTable->currentRow();
    if (currentRow >= 0 && currentRow < m_streamList.size()) {
        const StreamInfo& stream = m_streamList[currentRow];

        int ret = QMessageBox::question(this, "Remove Stream",
                                       QString("Are you sure you want to remove stream '%1'?").arg(stream.name),
                                       QMessageBox::Yes | QMessageBox::No);

        if (ret == QMessageBox::Yes) {
            emit streamRemoveRequested(stream.url);
            m_streamList.removeAt(currentRow);
            updateStreamsTable();
        }
    }
}

void ConfigurationDialog::onEditStreamClicked()
{
    int currentRow = m_streamsTable->currentRow();
    if (currentRow >= 0 && currentRow < m_streamList.size()) {
        StreamInfo& stream = m_streamList[currentRow];

        bool ok;
        QString name = QInputDialog::getText(this, "Edit Stream", "Stream Name:", QLineEdit::Normal, stream.name, &ok);
        if (!ok) return;

        QString url = QInputDialog::getText(this, "Edit Stream", "RTSP URL:", QLineEdit::Normal, stream.url, &ok);
        if (!ok) return;

        stream.name = name;
        stream.url = url;

        updateStreamsTable();
    }
}

void ConfigurationDialog::onTestConnectionClicked()
{
    QString address = m_serverAddressEdit->text();
    int port = m_serverPortSpinBox->value();

    m_testConnectionButton->setText("Testing...");
    m_testConnectionButton->setEnabled(false);

    // Simple connection test (placeholder)
    QTimer::singleShot(2000, this, [this]() {
        m_testConnectionButton->setText("Test Connection");
        m_testConnectionButton->setEnabled(true);

        QMessageBox::information(this, "Connection Test",
                               "Connection test completed.\n\nNote: This is a placeholder implementation.");
    });
}

void ConfigurationDialog::onRestoreDefaultsClicked()
{
    int ret = QMessageBox::question(this, "Restore Defaults",
                                   "Are you sure you want to restore all settings to their default values?",
                                   QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // Restore default values
        m_serverAddressEdit->setText("*************");
        m_serverPortSpinBox->setValue(8080);
        m_autoConnectCheckBox->setChecked(false);
        m_maxStreamsPerRowSpinBox->setValue(2);
        m_showOverlaysCheckBox->setChecked(true);
        m_showStatisticsCheckBox->setChecked(true);
        m_showStreamInfoCheckBox->setChecked(true);
        m_overlayStyleComboBox->setCurrentText("Modern");
        m_updateIntervalSpinBox->setValue(33);
        m_bufferSizeSpinBox->setValue(100);
        m_dropFramesCheckBox->setChecked(true);
        m_maxCpuUsageSpinBox->setValue(80);
        m_enableGpuAccelCheckBox->setChecked(true);
        m_reconnectIntervalSpinBox->setValue(3000);
        m_maxReconnectAttemptsSpinBox->setValue(5);

        updateServiceUrls();
    }
}

void ConfigurationDialog::onStreamSelectionChanged()
{
    bool hasSelection = m_streamsTable->currentRow() >= 0;
    m_editStreamButton->setEnabled(hasSelection);
    m_removeStreamButton->setEnabled(hasSelection);
}

void ConfigurationDialog::updateStreamsTable()
{
    m_streamsTable->setRowCount(m_streamList.size());

    for (int i = 0; i < m_streamList.size(); ++i) {
        const StreamInfo& stream = m_streamList[i];

        m_streamsTable->setItem(i, 0, new QTableWidgetItem(stream.name));
        m_streamsTable->setItem(i, 1, new QTableWidgetItem(stream.url));
        m_streamsTable->setItem(i, 2, new QTableWidgetItem(stream.username));

        QTableWidgetItem* enabledItem = new QTableWidgetItem();
        enabledItem->setCheckState(stream.enabled ? Qt::Checked : Qt::Unchecked);
        m_streamsTable->setItem(i, 3, enabledItem);
    }
}

void ConfigurationDialog::loadSettings()
{
    QSettings settings("C-AIBOX", "RTSPClient");

    // Load server settings
    m_serverAddressEdit->setText(settings.value("server/address", "*************").toString());
    m_serverPortSpinBox->setValue(settings.value("server/port", 8080).toInt());
    m_autoConnectCheckBox->setChecked(settings.value("server/auto_connect", false).toBool());

    // Load display settings
    m_maxStreamsPerRowSpinBox->setValue(settings.value("display/max_streams_per_row", 2).toInt());
    m_showOverlaysCheckBox->setChecked(settings.value("display/show_overlays", true).toBool());
    m_showStatisticsCheckBox->setChecked(settings.value("display/show_statistics", true).toBool());
    m_showStreamInfoCheckBox->setChecked(settings.value("display/show_stream_info", true).toBool());
    m_overlayStyleComboBox->setCurrentText(settings.value("display/overlay_style", "Modern").toString());
    m_updateIntervalSpinBox->setValue(settings.value("display/update_interval", 33).toInt());

    // Load advanced settings
    m_bufferSizeSpinBox->setValue(settings.value("advanced/buffer_size", 100).toInt());
    m_dropFramesCheckBox->setChecked(settings.value("advanced/drop_frames", true).toBool());
    m_maxCpuUsageSpinBox->setValue(settings.value("advanced/max_cpu_usage", 80).toInt());
    m_enableGpuAccelCheckBox->setChecked(settings.value("advanced/enable_gpu_accel", true).toBool());
    m_reconnectIntervalSpinBox->setValue(settings.value("advanced/reconnect_interval", 3000).toInt());
    m_maxReconnectAttemptsSpinBox->setValue(settings.value("advanced/max_reconnect_attempts", 5).toInt());

    updateServiceUrls();
}

void ConfigurationDialog::saveSettings()
{
    QSettings settings("C-AIBOX", "RTSPClient");

    // Save server settings
    settings.setValue("server/address", m_serverAddressEdit->text());
    settings.setValue("server/port", m_serverPortSpinBox->value());
    settings.setValue("server/auto_connect", m_autoConnectCheckBox->isChecked());

    // Save display settings
    settings.setValue("display/max_streams_per_row", m_maxStreamsPerRowSpinBox->value());
    settings.setValue("display/show_overlays", m_showOverlaysCheckBox->isChecked());
    settings.setValue("display/show_statistics", m_showStatisticsCheckBox->isChecked());
    settings.setValue("display/show_stream_info", m_showStreamInfoCheckBox->isChecked());
    settings.setValue("display/overlay_style", m_overlayStyleComboBox->currentText());
    settings.setValue("display/update_interval", m_updateIntervalSpinBox->value());

    // Save advanced settings
    settings.setValue("advanced/buffer_size", m_bufferSizeSpinBox->value());
    settings.setValue("advanced/drop_frames", m_dropFramesCheckBox->isChecked());
    settings.setValue("advanced/max_cpu_usage", m_maxCpuUsageSpinBox->value());
    settings.setValue("advanced/enable_gpu_accel", m_enableGpuAccelCheckBox->isChecked());
    settings.setValue("advanced/reconnect_interval", m_reconnectIntervalSpinBox->value());
    settings.setValue("advanced/max_reconnect_attempts", m_maxReconnectAttemptsSpinBox->value());
}

void ConfigurationDialog::validateSettings()
{
    // Validate server address
    if (m_serverAddressEdit->text().isEmpty()) {
        QMessageBox::warning(this, "Invalid Configuration", "Server address cannot be empty.");
        m_tabWidget->setCurrentWidget(m_serverTab);
        m_serverAddressEdit->setFocus();
        return;
    }

    // Validate port range
    if (m_serverPortSpinBox->value() < 1 || m_serverPortSpinBox->value() > 65535) {
        QMessageBox::warning(this, "Invalid Configuration", "Server port must be between 1 and 65535.");
        m_tabWidget->setCurrentWidget(m_serverTab);
        m_serverPortSpinBox->setFocus();
        return;
    }
}

#include "configuration_dialog.moc"
