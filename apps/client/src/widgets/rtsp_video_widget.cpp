#include "widgets/rtsp_video_widget.hpp"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QVideoWidget>
#include <QMediaPlayer>
#include <QMediaPlaylist>
#include <QPainter>
#include <QMouseEvent>
#include <QContextMenuEvent>
#include <QResizeEvent>
#include <QMenu>
#include <QAction>
#include <QTimer>
#include <QDateTime>
#include <QDebug>
#include <QMessageBox>
#include <QMutexLocker>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QUrl>
#include <QFont>
#include <QFontMetrics>

RTSPVideoWidget::RTSPVideoWidget(int streamId, const QString& streamUrl, const QString& streamName, QWidget *parent)
    : Q<PERSON>rame(parent)
    , m_streamId(streamId)
    , m_streamUrl(streamUrl)
    , m_streamName(streamName)
    , m_isSelected(false)
    , m_showOverlays(true)
    , m_showStatistics(true)
    , m_showStreamInfo(true)
    , m_layout(nullptr)
    , m_titleLabel(nullptr)
    , m_statusLabel(nullptr)
    , m_toggleButton(nullptr)
    , m_videoWidget(nullptr)
    , m_mediaPlayer(nullptr)
    , m_playlist(nullptr)
    , m_statsUpdateTimer(nullptr)
    , m_reconnectTimer(nullptr)
    , m_cleanupTimer(nullptr)
    , m_contextMenu(nullptr)
    , m_startAction(nullptr)
    , m_stopAction(nullptr)
    , m_restartAction(nullptr)
    , m_infoAction(nullptr)
    , m_reconnectAction(nullptr)
    , m_networkManager(nullptr)
    , m_isReconnecting(false)
    , m_reconnectAttempts(0)
{
    setMinimumSize(320, 240);
    setFrameStyle(QFrame::Box | QFrame::Raised);
    setLineWidth(2);
    setProperty("class", "rtsp-stream-widget");

    setupUI();
    setupMediaPlayer();
    setupContextMenu();

    // Initialize statistics
    m_stats.streamUrl = streamUrl;
    m_stats.status = "Stopped";
    m_stats.lastUpdate = QDateTime::currentDateTime();

    // Setup timers
    m_statsUpdateTimer = new QTimer(this);
    connect(m_statsUpdateTimer, &QTimer::timeout, this, &RTSPVideoWidget::onUpdateStats);
    m_statsUpdateTimer->start(2000); // Update every 2 seconds

    m_reconnectTimer = new QTimer(this);
    m_reconnectTimer->setSingleShot(true);
    connect(m_reconnectTimer, &QTimer::timeout, this, &RTSPVideoWidget::onReconnectStream);

    m_cleanupTimer = new QTimer(this);
    connect(m_cleanupTimer, &QTimer::timeout, this, &RTSPVideoWidget::cleanupOldFaceResults);
    m_cleanupTimer->start(1000); // Cleanup every second

    // Setup network manager for server communication
    m_networkManager = new QNetworkAccessManager(this);

    qDebug() << "Created RTSP video widget for stream:" << streamName << "URL:" << streamUrl;
}

RTSPVideoWidget::~RTSPVideoWidget()
{
    if (m_mediaPlayer) {
        m_mediaPlayer->stop();
    }
    
    if (m_statsUpdateTimer) {
        m_statsUpdateTimer->stop();
    }
    
    if (m_reconnectTimer) {
        m_reconnectTimer->stop();
    }
    
    if (m_cleanupTimer) {
        m_cleanupTimer->stop();
    }
}

void RTSPVideoWidget::setupUI()
{
    m_layout = new QVBoxLayout(this);
    m_layout->setContentsMargins(5, 5, 5, 5);
    m_layout->setSpacing(2);

    // Title label
    m_titleLabel = new QLabel(m_streamName);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setStyleSheet("font-weight: bold; color: #4da8da; padding: 5px; background-color: rgba(0,0,0,0.7); border-radius: 3px;");
    m_layout->addWidget(m_titleLabel);

    // Video widget
    m_videoWidget = new QVideoWidget();
    m_videoWidget->setMinimumHeight(180);
    m_videoWidget->setStyleSheet("background-color: black;");
    m_layout->addWidget(m_videoWidget);

    // Bottom controls
    QHBoxLayout* controlsLayout = new QHBoxLayout();
    
    m_statusLabel = new QLabel("Stopped");
    m_statusLabel->setStyleSheet("color: #ff6b6b; font-weight: bold; padding: 2px;");
    controlsLayout->addWidget(m_statusLabel);

    controlsLayout->addStretch();

    m_toggleButton = new QPushButton("Start");
    m_toggleButton->setMaximumWidth(60);
    m_toggleButton->setStyleSheet("QPushButton { background-color: #4da8da; color: white; border: none; border-radius: 3px; padding: 5px; font-weight: bold; }");
    connect(m_toggleButton, &QPushButton::clicked, this, &RTSPVideoWidget::onToggleStream);
    controlsLayout->addWidget(m_toggleButton);

    m_layout->addLayout(controlsLayout);
}

void RTSPVideoWidget::setupMediaPlayer()
{
    m_mediaPlayer = new QMediaPlayer(this);
    m_playlist = new QMediaPlaylist(this);
    
    // Set video output
    m_mediaPlayer->setVideoOutput(m_videoWidget);
    m_mediaPlayer->setPlaylist(m_playlist);
    
    // Connect signals
    connect(m_mediaPlayer, &QMediaPlayer::mediaStatusChanged, this, &RTSPVideoWidget::onMediaStatusChanged);
    connect(m_mediaPlayer, &QMediaPlayer::stateChanged, this, &RTSPVideoWidget::onPlayerStateChanged);
    connect(m_mediaPlayer, QOverload<QMediaPlayer::Error>::of(&QMediaPlayer::error), this, &RTSPVideoWidget::onPlayerError);
    connect(m_mediaPlayer, &QMediaPlayer::positionChanged, this, &RTSPVideoWidget::onPositionChanged);
    connect(m_mediaPlayer, &QMediaPlayer::durationChanged, this, &RTSPVideoWidget::onDurationChanged);
    
    // Configure playlist
    m_playlist->setPlaybackMode(QMediaPlaylist::Loop);
}

void RTSPVideoWidget::setupContextMenu()
{
    m_contextMenu = new QMenu(this);

    m_startAction = m_contextMenu->addAction("Start Stream");
    connect(m_startAction, &QAction::triggered, this, &RTSPVideoWidget::startStream);

    m_stopAction = m_contextMenu->addAction("Stop Stream");
    connect(m_stopAction, &QAction::triggered, this, &RTSPVideoWidget::stopStream);

    m_restartAction = m_contextMenu->addAction("Restart Stream");
    connect(m_restartAction, &QAction::triggered, this, &RTSPVideoWidget::restartStream);

    m_reconnectAction = m_contextMenu->addAction("Reconnect");
    connect(m_reconnectAction, &QAction::triggered, this, &RTSPVideoWidget::onReconnectStream);

    m_contextMenu->addSeparator();

    m_infoAction = m_contextMenu->addAction("Stream Info...");
    connect(m_infoAction, &QAction::triggered, this, &RTSPVideoWidget::onShowStreamInfo);
}

void RTSPVideoWidget::startStream()
{
    if (m_streamUrl.isEmpty()) {
        qWarning() << "Cannot start stream: URL is empty";
        return;
    }

    qDebug() << "Starting RTSP stream:" << m_streamUrl;

    // Clear playlist and add stream URL
    m_playlist->clear();
    m_playlist->addMedia(QUrl(m_streamUrl));
    
    // Start playback
    m_mediaPlayer->play();
    
    // Update UI
    m_toggleButton->setText("Stop");
    m_statusLabel->setText("Connecting...");
    m_statusLabel->setStyleSheet("color: #ffa500; font-weight: bold; padding: 2px;");
    
    // Update statistics
    m_stats.status = "Connecting";
    m_stats.playerState = QMediaPlayer::PlayingState;
    m_stats.lastUpdate = QDateTime::currentDateTime();
    
    emit streamStatusChanged(m_streamId, "Connecting");
}

void RTSPVideoWidget::stopStream()
{
    qDebug() << "Stopping RTSP stream:" << m_streamUrl;

    m_mediaPlayer->stop();
    
    // Update UI
    m_toggleButton->setText("Start");
    m_statusLabel->setText("Stopped");
    m_statusLabel->setStyleSheet("color: #ff6b6b; font-weight: bold; padding: 2px;");
    
    // Clear face results
    QMutexLocker locker(&m_faceResultsMutex);
    m_faceResults.clear();
    
    // Update statistics
    m_stats.status = "Stopped";
    m_stats.playerState = QMediaPlayer::StoppedState;
    m_stats.lastUpdate = QDateTime::currentDateTime();
    
    emit streamStatusChanged(m_streamId, "Stopped");
    
    update(); // Trigger repaint to clear overlays
}

void RTSPVideoWidget::restartStream()
{
    stopStream();
    QTimer::singleShot(1000, this, &RTSPVideoWidget::startStream);
}

bool RTSPVideoWidget::isStreaming() const
{
    return m_mediaPlayer && (m_mediaPlayer->state() == QMediaPlayer::PlayingState);
}

void RTSPVideoWidget::setStreamUrl(const QString& url)
{
    bool wasStreaming = isStreaming();
    
    if (wasStreaming) {
        stopStream();
    }
    
    m_streamUrl = url;
    m_stats.streamUrl = url;
    
    if (wasStreaming) {
        QTimer::singleShot(500, this, &RTSPVideoWidget::startStream);
    }
}

void RTSPVideoWidget::setStreamName(const QString& name)
{
    m_streamName = name;
    m_titleLabel->setText(name);
}

void RTSPVideoWidget::setShowOverlays(bool show)
{
    m_showOverlays = show;
    update();
}

void RTSPVideoWidget::setShowStatistics(bool show)
{
    m_showStatistics = show;
    update();
}

void RTSPVideoWidget::setShowStreamInfo(bool show)
{
    m_showStreamInfo = show;
    update();
}

void RTSPVideoWidget::addFaceDetectionResult(const FaceDetectionResult& result)
{
    QMutexLocker locker(&m_faceResultsMutex);
    m_faceResults.append(result);
    
    // Trigger repaint to show new overlay
    update();
}

void RTSPVideoWidget::clearFaceDetectionResults()
{
    QMutexLocker locker(&m_faceResultsMutex);
    m_faceResults.clear();
    update();
}

// Event handlers
void RTSPVideoWidget::paintEvent(QPaintEvent* event)
{
    QFrame::paintEvent(event);

    if (!m_showOverlays && !m_showStatistics && !m_showStreamInfo) {
        return;
    }

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // Get video widget geometry for overlay positioning
    QRect videoRect = m_videoWidget->geometry();

    if (m_showOverlays) {
        drawFaceOverlays(painter);
    }

    if (m_showStreamInfo) {
        drawStreamInfo(painter);
    }

    if (m_showStatistics) {
        drawStatistics(painter);
    }
}

void RTSPVideoWidget::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        m_isSelected = !m_isSelected;
        emit streamClicked(m_streamId);
        update();
    }
    QFrame::mousePressEvent(event);
}

void RTSPVideoWidget::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        emit streamDoubleClicked(m_streamId);
    }
    QFrame::mouseDoubleClickEvent(event);
}

void RTSPVideoWidget::contextMenuEvent(QContextMenuEvent* event)
{
    // Update context menu actions based on current state
    bool streaming = isStreaming();
    m_startAction->setEnabled(!streaming);
    m_stopAction->setEnabled(streaming);
    m_restartAction->setEnabled(streaming);
    m_reconnectAction->setEnabled(!streaming || m_isReconnecting);

    emit streamContextMenu(m_streamId, event->globalPos());
    m_contextMenu->exec(event->globalPos());
}

void RTSPVideoWidget::resizeEvent(QResizeEvent* event)
{
    QFrame::resizeEvent(event);
    update(); // Trigger repaint for overlays
}

// Media player slots
void RTSPVideoWidget::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    QString statusText;
    QString statusColor = "#ffa500"; // Orange by default

    switch (status) {
        case QMediaPlayer::LoadingMedia:
            statusText = "Loading...";
            break;
        case QMediaPlayer::LoadedMedia:
            statusText = "Loaded";
            statusColor = "#4da8da"; // Blue
            break;
        case QMediaPlayer::BufferingMedia:
            statusText = "Buffering...";
            break;
        case QMediaPlayer::BufferedMedia:
            statusText = "Streaming";
            statusColor = "#51cf66"; // Green
            m_reconnectAttempts = 0; // Reset reconnect attempts on success
            m_isReconnecting = false;
            break;
        case QMediaPlayer::EndOfMedia:
            statusText = "End of Stream";
            statusColor = "#ff6b6b"; // Red
            break;
        case QMediaPlayer::InvalidMedia:
            statusText = "Invalid Media";
            statusColor = "#ff6b6b"; // Red
            if (!m_isReconnecting && m_reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                scheduleReconnect();
            }
            break;
        default:
            statusText = "Unknown";
            break;
    }

    m_statusLabel->setText(statusText);
    m_statusLabel->setStyleSheet(QString("color: %1; font-weight: bold; padding: 2px;").arg(statusColor));

    m_stats.status = statusText;
    m_stats.lastUpdate = QDateTime::currentDateTime();

    emit streamStatusChanged(m_streamId, statusText);
}

void RTSPVideoWidget::onPlayerStateChanged(QMediaPlayer::State state)
{
    m_stats.playerState = state;

    switch (state) {
        case QMediaPlayer::StoppedState:
            m_toggleButton->setText("Start");
            break;
        case QMediaPlayer::PlayingState:
            m_toggleButton->setText("Stop");
            break;
        case QMediaPlayer::PausedState:
            m_toggleButton->setText("Resume");
            break;
    }
}

void RTSPVideoWidget::onPlayerError(QMediaPlayer::Error error)
{
    QString errorText;

    switch (error) {
        case QMediaPlayer::ResourceError:
            errorText = "Resource Error";
            break;
        case QMediaPlayer::FormatError:
            errorText = "Format Error";
            break;
        case QMediaPlayer::NetworkError:
            errorText = "Network Error";
            break;
        case QMediaPlayer::AccessDeniedError:
            errorText = "Access Denied";
            break;
        case QMediaPlayer::ServiceMissingError:
            errorText = "Service Missing";
            break;
        default:
            errorText = "Unknown Error";
            break;
    }

    qWarning() << "Media player error for stream" << m_streamId << ":" << errorText;

    m_statusLabel->setText(errorText);
    m_statusLabel->setStyleSheet("color: #ff6b6b; font-weight: bold; padding: 2px;");

    m_stats.status = errorText;
    m_stats.lastUpdate = QDateTime::currentDateTime();

    emit streamError(m_streamId, errorText);

    // Schedule reconnect for network errors
    if (error == QMediaPlayer::NetworkError && !m_isReconnecting && m_reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        scheduleReconnect();
    }
}

void RTSPVideoWidget::onPositionChanged(qint64 position)
{
    Q_UNUSED(position)
    // Update frame count (approximate)
    m_stats.frameCount++;
}

void RTSPVideoWidget::onDurationChanged(qint64 duration)
{
    Q_UNUSED(duration)
    // For live streams, duration is usually 0 or -1
}

void RTSPVideoWidget::onUpdateStats()
{
    updateStreamStats();
}

void RTSPVideoWidget::onToggleStream()
{
    if (isStreaming()) {
        stopStream();
    } else {
        startStream();
    }
}

void RTSPVideoWidget::onShowStreamInfo()
{
    QString info = QString("Stream Information\n\n"
                          "Stream ID: %1\n"
                          "Name: %2\n"
                          "URL: %3\n"
                          "Status: %4\n"
                          "Player State: %5\n"
                          "Frame Count: %6\n"
                          "Reconnect Attempts: %7\n"
                          "Last Update: %8")
                   .arg(m_streamId)
                   .arg(m_streamName)
                   .arg(m_streamUrl)
                   .arg(m_stats.status)
                   .arg(m_stats.playerState)
                   .arg(m_stats.frameCount)
                   .arg(m_reconnectAttempts)
                   .arg(m_stats.lastUpdate.toString());

    QMessageBox::information(this, "Stream Information", info);
}

void RTSPVideoWidget::onReconnectStream()
{
    if (m_reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        qWarning() << "Max reconnect attempts reached for stream" << m_streamId;
        return;
    }

    m_reconnectAttempts++;
    m_isReconnecting = true;

    qDebug() << "Reconnecting stream" << m_streamId << "attempt" << m_reconnectAttempts;

    m_statusLabel->setText(QString("Reconnecting... (%1/%2)").arg(m_reconnectAttempts).arg(MAX_RECONNECT_ATTEMPTS));
    m_statusLabel->setStyleSheet("color: #ffa500; font-weight: bold; padding: 2px;");

    restartStream();
}

void RTSPVideoWidget::scheduleReconnect()
{
    if (!m_isReconnecting && m_reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        m_isReconnecting = true;
        m_reconnectTimer->start(RECONNECT_DELAY_MS);

        qDebug() << "Scheduled reconnect for stream" << m_streamId << "in" << RECONNECT_DELAY_MS << "ms";
    }
}

// Helper methods
void RTSPVideoWidget::drawFaceOverlays(QPainter& painter)
{
    if (!m_showOverlays) return;

    QMutexLocker locker(&m_faceResultsMutex);

    if (m_faceResults.isEmpty()) return;

    QRect videoRect = m_videoWidget->geometry();

    painter.setPen(QPen(Qt::green, 2));
    painter.setFont(QFont("Arial", 10, QFont::Bold));

    for (const FaceDetectionResult& face : m_faceResults) {
        // Adjust bounding box to video widget coordinates
        QRect adjustedBox = face.boundingBox;
        adjustedBox.translate(videoRect.topLeft());

        // Ensure box is within video widget bounds
        adjustedBox = adjustedBox.intersected(videoRect);

        if (adjustedBox.isEmpty()) continue;

        // Draw bounding box
        painter.setPen(QPen(face.overlayColor, 2));
        painter.drawRect(adjustedBox);

        // Draw person information
        QString infoText = QString("%1 (%2%)").arg(face.personName).arg(int(face.confidence * 100));
        if (!face.department.isEmpty()) {
            infoText += QString("\n%1").arg(face.department);
        }

        // Draw text background
        QFontMetrics fm(painter.font());
        QRect textRect = fm.boundingRect(adjustedBox.topLeft().x(), adjustedBox.topLeft().y() - 30,
                                        200, 50, Qt::AlignLeft | Qt::TextWordWrap, infoText);
        textRect.adjust(-2, -2, 2, 2);

        painter.fillRect(textRect, QColor(0, 0, 0, 180));

        // Draw text
        painter.setPen(face.isAuthorized ? Qt::green : Qt::red);
        painter.drawText(textRect, Qt::AlignLeft | Qt::TextWordWrap, infoText);
    }
}

void RTSPVideoWidget::drawStreamInfo(QPainter& painter)
{
    if (!m_showStreamInfo || !isStreaming()) return;

    QRect videoRect = m_videoWidget->geometry();

    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 8));

    QString infoText = QString("Stream %1\n%2\n%3")
                      .arg(m_streamId)
                      .arg(m_streamName)
                      .arg(m_stats.status);

    QFontMetrics fm(painter.font());
    QRect textRect = fm.boundingRect(infoText);
    textRect.moveTopRight(QPoint(videoRect.right() - 5, videoRect.top() + 5));
    textRect.adjust(-2, -2, 2, 2);

    painter.fillRect(textRect, QColor(0, 0, 0, 150));
    painter.drawText(textRect, Qt::AlignLeft, infoText);
}

void RTSPVideoWidget::drawStatistics(QPainter& painter)
{
    if (!m_showStatistics || !isStreaming()) return;

    QRect videoRect = m_videoWidget->geometry();

    painter.setPen(Qt::yellow);
    painter.setFont(QFont("Arial", 8));

    QString statsText = QString("Frames: %1\nFPS: %2\nStatus: %3")
                       .arg(m_stats.frameCount)
                       .arg(m_stats.fps)
                       .arg(m_stats.status);

    QFontMetrics fm(painter.font());
    QRect textRect = fm.boundingRect(statsText);
    textRect.moveBottomLeft(QPoint(videoRect.left() + 5, videoRect.bottom() - 5));
    textRect.adjust(-2, -2, 2, 2);

    painter.fillRect(textRect, QColor(0, 0, 0, 150));
    painter.drawText(textRect, Qt::AlignLeft, statsText);
}

void RTSPVideoWidget::updateStreamStats()
{
    if (isStreaming()) {
        // Estimate FPS based on frame count changes
        static int lastFrameCount = 0;
        static QDateTime lastUpdate = QDateTime::currentDateTime();

        QDateTime now = QDateTime::currentDateTime();
        int framesDelta = m_stats.frameCount - lastFrameCount;
        qint64 timeDelta = lastUpdate.msecsTo(now);

        if (timeDelta > 0) {
            m_stats.fps = (framesDelta * 1000) / timeDelta;
        }

        lastFrameCount = m_stats.frameCount;
        lastUpdate = now;

        m_stats.lastUpdate = now;
    }
}

void RTSPVideoWidget::cleanupOldFaceResults()
{
    QMutexLocker locker(&m_faceResultsMutex);

    QDateTime now = QDateTime::currentDateTime();

    // Remove face results older than FACE_RESULT_TIMEOUT_MS
    m_faceResults.erase(
        std::remove_if(m_faceResults.begin(), m_faceResults.end(),
                      [now](const FaceDetectionResult& result) {
                          return result.timestamp.msecsTo(now) > FACE_RESULT_TIMEOUT_MS;
                      }),
        m_faceResults.end()
    );

    // Trigger repaint if we removed any results
    if (!m_faceResults.isEmpty()) {
        update();
    }
}

#include "rtsp_video_widget.moc"
