#include "widgets/rtsp_stream_widget.hpp"
#include <QPainter>
#include <QPaintEvent>
#include <QMouseEvent>
#include <QContextMenuEvent>
#include <QResizeEvent>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QProgressBar>
#include <QTimer>
#include <QMenu>
#include <QAction>
#include <QDateTime>
#include <QDebug>
#include <QRandomGenerator>
#include <QFont>
#include <QFontMetrics>
#include <QtMath>

RTSPStreamWidget::RTSPStreamWidget(int streamId, const QString& streamUrl, QWidget *parent)
    : Q<PERSON>rame(parent)
    , m_streamId(streamId)
    , m_streamUrl(streamUrl)
    , m_streamTitle(QString("Stream %1").arg(streamId))
    , m_isStreaming(false)
    , m_isSelected(false)
    , m_showOverlays(true)
    , m_showStatistics(true)
    , m_showStreamInfo(true)
    , m_layout(nullptr)
    , m_titleLabel(nullptr)
    , m_statusLabel(nullptr)
    , m_toggleButton(nullptr)
    , m_bufferBar(nullptr)
    , m_frameUpdateTimer(nullptr)
    , m_statsUpdateTimer(nullptr)
    , m_contextMenu(nullptr)
    , m_startAction(nullptr)
    , m_stopAction(nullptr)
    , m_restartAction(nullptr)
    , m_infoAction(nullptr)
    , m_removeAction(nullptr)
    , m_mockFrameCounter(0)
    , m_mockBackgroundColor(QColor(30, 42, 68))
{
    setMinimumSize(320, 240);
    setMaximumSize(640, 480);
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    setFrameStyle(QFrame::Box | QFrame::Raised);
    setLineWidth(2);
    setProperty("class", "camera-feed");

    setupUI();
    setupContextMenu();

    // Initialize mock frame
    generateMockFrame();

    // Setup timers
    m_frameUpdateTimer = new QTimer(this);
    connect(m_frameUpdateTimer, &QTimer::timeout, this, &RTSPStreamWidget::onUpdateFrame);

    m_statsUpdateTimer = new QTimer(this);
    connect(m_statsUpdateTimer, &QTimer::timeout, this, &RTSPStreamWidget::onUpdateStats);
    m_statsUpdateTimer->start(1000); // Update stats every second

    // Initialize stats
    m_stats.streamUrl = streamUrl;
    m_stats.status = "Stopped";
    m_stats.lastUpdate = QDateTime::currentDateTime();
}

RTSPStreamWidget::~RTSPStreamWidget()
{
    if (m_frameUpdateTimer) {
        m_frameUpdateTimer->stop();
    }
    if (m_statsUpdateTimer) {
        m_statsUpdateTimer->stop();
    }
}

void RTSPStreamWidget::setupUI()
{
    m_layout = new QVBoxLayout(this);
    m_layout->setContentsMargins(5, 5, 5, 5);
    m_layout->setSpacing(2);

    // Title label
    m_titleLabel = new QLabel(m_streamTitle);
    m_titleLabel->setProperty("class", "camera-header");
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_layout->addWidget(m_titleLabel);

    // Add stretch to push controls to bottom
    m_layout->addStretch();

    // Bottom controls
    QHBoxLayout* controlsLayout = new QHBoxLayout();
    
    m_statusLabel = new QLabel("Stopped");
    m_statusLabel->setProperty("class", "status-info");
    controlsLayout->addWidget(m_statusLabel);

    controlsLayout->addStretch();

    m_toggleButton = new QPushButton("Start");
    m_toggleButton->setMaximumWidth(60);
    connect(m_toggleButton, &QPushButton::clicked, this, &RTSPStreamWidget::onToggleStream);
    controlsLayout->addWidget(m_toggleButton);

    m_layout->addLayout(controlsLayout);

    // Buffer progress bar
    m_bufferBar = new QProgressBar();
    m_bufferBar->setMaximumHeight(4);
    m_bufferBar->setRange(0, 100);
    m_bufferBar->setValue(0);
    m_layout->addWidget(m_bufferBar);
}

void RTSPStreamWidget::setupContextMenu()
{
    m_contextMenu = new QMenu(this);

    m_startAction = m_contextMenu->addAction("Start Stream");
    connect(m_startAction, &QAction::triggered, this, &RTSPStreamWidget::startStream);

    m_stopAction = m_contextMenu->addAction("Stop Stream");
    connect(m_stopAction, &QAction::triggered, this, &RTSPStreamWidget::stopStream);

    m_restartAction = m_contextMenu->addAction("Restart Stream");
    connect(m_restartAction, &QAction::triggered, this, &RTSPStreamWidget::restartStream);

    m_contextMenu->addSeparator();

    m_infoAction = m_contextMenu->addAction("Stream Info...");
    connect(m_infoAction, &QAction::triggered, this, &RTSPStreamWidget::onShowStreamInfo);

    m_removeAction = m_contextMenu->addAction("Remove Stream");
    // Remove action will be connected by parent widget
}

void RTSPStreamWidget::startStream()
{
    if (!m_isStreaming) {
        m_isStreaming = true;
        m_stats.status = "Streaming";
        m_statusLabel->setText("Streaming");
        m_statusLabel->setProperty("class", "status-success");
        m_toggleButton->setText("Stop");
        
        // Start frame updates
        m_frameUpdateTimer->start(33); // ~30 FPS
        
        // Generate some mock faces
        generateMockFaces();
        
        emit streamStatusChanged(m_streamId, "Streaming");
        
        qDebug() << "Started stream" << m_streamId << ":" << m_streamUrl;
    }
}

void RTSPStreamWidget::stopStream()
{
    if (m_isStreaming) {
        m_isStreaming = false;
        m_stats.status = "Stopped";
        m_statusLabel->setText("Stopped");
        m_statusLabel->setProperty("class", "status-error");
        m_toggleButton->setText("Start");
        
        // Stop frame updates
        m_frameUpdateTimer->stop();
        
        // Clear face detections
        m_faceDetections.clear();
        
        emit streamStatusChanged(m_streamId, "Stopped");
        
        qDebug() << "Stopped stream" << m_streamId << ":" << m_streamUrl;
    }
}

void RTSPStreamWidget::restartStream()
{
    stopStream();
    QTimer::singleShot(500, this, &RTSPStreamWidget::startStream);
}

void RTSPStreamWidget::setStreamUrl(const QString& url)
{
    m_streamUrl = url;
    m_stats.streamUrl = url;
}

void RTSPStreamWidget::setShowOverlays(bool show)
{
    m_showOverlays = show;
    update();
}

void RTSPStreamWidget::setShowStatistics(bool show)
{
    m_showStatistics = show;
    update();
}

void RTSPStreamWidget::setStreamTitle(const QString& title)
{
    m_streamTitle = title;
    m_titleLabel->setText(title);
}

void RTSPStreamWidget::addMockFaceDetection(const FaceDetection& face)
{
    m_faceDetections.append(face);
    update();
}

void RTSPStreamWidget::clearFaceDetections()
{
    m_faceDetections.clear();
    update();
}

void RTSPStreamWidget::updateMockFrame()
{
    if (m_isStreaming) {
        generateMockFrame();
        update();
    }
}

void RTSPStreamWidget::paintEvent(QPaintEvent* event)
{
    QFrame::paintEvent(event);
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // Calculate video area (excluding title and controls)
    QRect titleRect = m_titleLabel->geometry();
    QRect controlsRect = QRect(0, height() - 50, width(), 50);
    m_videoRect = QRect(5, titleRect.bottom() + 5, width() - 10, 
                       controlsRect.top() - titleRect.bottom() - 10);

    if (m_isStreaming) {
        drawVideoFrame(painter);
        if (m_showOverlays) {
            drawFaceOverlays(painter);
        }
        if (m_showStatistics) {
            drawStatistics(painter);
        }
    } else {
        // Draw "No Signal" message
        painter.fillRect(m_videoRect, QColor(20, 20, 20));
        painter.setPen(QColor(128, 128, 128));
        painter.setFont(QFont("Arial", 14));
        painter.drawText(m_videoRect, Qt::AlignCenter, "No Signal\nClick Start to begin streaming");
    }

    if (m_showStreamInfo) {
        drawStreamInfo(painter);
    }
}

void RTSPStreamWidget::drawVideoFrame(QPainter& painter)
{
    if (!m_currentFrame.isNull()) {
        // Scale frame to fit video rect while maintaining aspect ratio
        QPixmap scaledFrame = m_currentFrame.scaled(m_videoRect.size(), 
                                                   Qt::KeepAspectRatio, 
                                                   Qt::SmoothTransformation);
        
        // Center the frame in the video rect
        QRect frameRect = scaledFrame.rect();
        frameRect.moveCenter(m_videoRect.center());
        
        painter.drawPixmap(frameRect, scaledFrame);
    } else {
        painter.fillRect(m_videoRect, m_mockBackgroundColor);
    }
}

void RTSPStreamWidget::drawFaceOverlays(QPainter& painter)
{
    painter.setPen(QPen(Qt::green, 2));
    painter.setFont(QFont("Arial", 10, QFont::Bold));

    for (const FaceDetection& face : m_faceDetections) {
        // Adjust bounding box to video rect coordinates
        QRect adjustedBox = face.boundingBox;
        adjustedBox.translate(m_videoRect.topLeft());

        // Draw bounding box
        painter.setPen(QPen(face.overlayColor, 2));
        painter.drawRect(adjustedBox);

        // Draw confidence and person info
        QString infoText = QString("%1 (%2%)").arg(face.personName).arg(int(face.confidence * 100));
        if (!face.department.isEmpty()) {
            infoText += QString("\n%1").arg(face.department);
        }

        // Draw text background
        QFontMetrics fm(painter.font());
        QRect textRect = fm.boundingRect(adjustedBox.topLeft().x(), adjustedBox.topLeft().y() - 30,
                                        200, 50, Qt::AlignLeft | Qt::TextWordWrap, infoText);
        textRect.adjust(-2, -2, 2, 2);

        painter.fillRect(textRect, QColor(0, 0, 0, 180));

        // Draw text
        painter.setPen(face.isAuthorized ? Qt::green : Qt::red);
        painter.drawText(textRect, Qt::AlignLeft | Qt::TextWordWrap, infoText);
    }
}

void RTSPStreamWidget::drawStreamInfo(QPainter& painter)
{
    if (!m_isStreaming) return;

    // Draw stream info in top-right corner
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 8));

    QString infoText = QString("Stream %1\n%2x%3 @ %4 FPS")
                      .arg(m_streamId)
                      .arg(m_videoRect.width())
                      .arg(m_videoRect.height())
                      .arg(m_stats.fps);

    QFontMetrics fm(painter.font());
    QRect textRect = fm.boundingRect(infoText);
    textRect.moveTopRight(QPoint(m_videoRect.right() - 5, m_videoRect.top() + 5));
    textRect.adjust(-2, -2, 2, 2);

    painter.fillRect(textRect, QColor(0, 0, 0, 150));
    painter.drawText(textRect, Qt::AlignLeft, infoText);
}

void RTSPStreamWidget::drawStatistics(QPainter& painter)
{
    if (!m_isStreaming || !m_showStatistics) return;

    // Draw statistics in bottom-left corner
    painter.setPen(Qt::yellow);
    painter.setFont(QFont("Arial", 8));

    QString statsText = QString("Bitrate: %1 kbps\nFrames: %2\nDropped: %3")
                       .arg(m_stats.bitrate)
                       .arg(m_stats.frameCount)
                       .arg(m_stats.droppedFrames);

    QFontMetrics fm(painter.font());
    QRect textRect = fm.boundingRect(statsText);
    textRect.moveBottomLeft(QPoint(m_videoRect.left() + 5, m_videoRect.bottom() - 5));
    textRect.adjust(-2, -2, 2, 2);

    painter.fillRect(textRect, QColor(0, 0, 0, 150));
    painter.drawText(textRect, Qt::AlignLeft, statsText);
}

void RTSPStreamWidget::generateMockFrame()
{
    // Create a simple animated frame
    m_currentFrame = QPixmap(640, 480);
    m_currentFrame.fill(m_mockBackgroundColor);

    QPainter framePainter(&m_currentFrame);
    framePainter.setRenderHint(QPainter::Antialiasing);

    // Draw some animated elements
    int time = m_mockFrameCounter++;

    // Moving circle
    int circleX = (time * 2) % 640;
    int circleY = 240 + 100 * qSin(time * 0.1);
    framePainter.setBrush(QColor(100, 150, 255, 100));
    framePainter.setPen(Qt::NoPen);
    framePainter.drawEllipse(circleX - 20, circleY - 20, 40, 40);

    // Grid pattern
    framePainter.setPen(QPen(QColor(60, 80, 120), 1));
    for (int x = 0; x < 640; x += 50) {
        framePainter.drawLine(x, 0, x, 480);
    }
    for (int y = 0; y < 480; y += 50) {
        framePainter.drawLine(0, y, 640, y);
    }

    // Timestamp
    framePainter.setPen(Qt::white);
    framePainter.setFont(QFont("Arial", 12));
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    framePainter.drawText(10, 30, timestamp);

    // Stream ID
    framePainter.drawText(10, 50, QString("Stream %1").arg(m_streamId));
}

void RTSPStreamWidget::generateMockFaces()
{
    m_faceDetections.clear();

    // Generate 1-3 random faces
    int faceCount = QRandomGenerator::global()->bounded(1, 4);

    for (int i = 0; i < faceCount; ++i) {
        FaceDetection face;

        // Random position within video area
        int x = QRandomGenerator::global()->bounded(50, m_videoRect.width() - 150);
        int y = QRandomGenerator::global()->bounded(50, m_videoRect.height() - 150);
        face.boundingBox = QRect(x, y, 100, 120);

        // Random confidence
        face.confidence = 0.7f + (QRandomGenerator::global()->bounded(30) / 100.0f);

        // Mock person data
        QStringList names = {"John Doe", "Jane Smith", "Bob Johnson", "Alice Brown", "Charlie Wilson"};
        QStringList departments = {"Engineering", "Security", "Management", "HR", "Operations"};

        face.personName = names[QRandomGenerator::global()->bounded(names.size())];
        face.department = departments[QRandomGenerator::global()->bounded(departments.size())];
        face.personId = QString("ID_%1").arg(QRandomGenerator::global()->bounded(1000, 9999));
        face.isAuthorized = QRandomGenerator::global()->bounded(100) > 20; // 80% authorized
        face.overlayColor = face.isAuthorized ? Qt::green : Qt::red;

        m_faceDetections.append(face);
    }
}

void RTSPStreamWidget::updateStreamStats()
{
    if (m_isStreaming) {
        m_stats.frameCount++;
        m_stats.fps = 30; // Mock 30 FPS
        m_stats.bitrate = 2000 + QRandomGenerator::global()->bounded(500); // 2-2.5 Mbps
        m_stats.droppedFrames += QRandomGenerator::global()->bounded(100) > 95 ? 1 : 0; // Occasional drops
        m_stats.cpuUsage = 15.0f + (QRandomGenerator::global()->bounded(20) / 10.0f);
        m_stats.memoryUsage = 45.0f + (QRandomGenerator::global()->bounded(10) / 10.0f);
        m_stats.lastUpdate = QDateTime::currentDateTime();

        // Update buffer bar
        int bufferLevel = 80 + QRandomGenerator::global()->bounded(20);
        m_bufferBar->setValue(bufferLevel);
    }
}

// Event handlers
void RTSPStreamWidget::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        m_isSelected = !m_isSelected;
        emit streamClicked(m_streamId);
        update();
    }
    QFrame::mousePressEvent(event);
}

void RTSPStreamWidget::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        emit streamDoubleClicked(m_streamId);
    }
    QFrame::mouseDoubleClickEvent(event);
}

void RTSPStreamWidget::contextMenuEvent(QContextMenuEvent* event)
{
    // Update context menu actions based on current state
    m_startAction->setEnabled(!m_isStreaming);
    m_stopAction->setEnabled(m_isStreaming);
    m_restartAction->setEnabled(m_isStreaming);

    emit streamContextMenu(m_streamId, event->globalPos());
    m_contextMenu->exec(event->globalPos());
}

void RTSPStreamWidget::resizeEvent(QResizeEvent* event)
{
    QFrame::resizeEvent(event);
    // Regenerate mock faces when widget is resized
    if (m_isStreaming && !m_faceDetections.isEmpty()) {
        generateMockFaces();
    }
}

// Slot implementations
void RTSPStreamWidget::onUpdateFrame()
{
    if (m_isStreaming) {
        generateMockFrame();

        // Occasionally update face positions
        if (m_mockFrameCounter % 90 == 0) { // Every 3 seconds at 30 FPS
            generateMockFaces();
        }

        update();
    }
}

void RTSPStreamWidget::onUpdateStats()
{
    updateStreamStats();
}

void RTSPStreamWidget::onToggleStream()
{
    if (m_isStreaming) {
        stopStream();
    } else {
        startStream();
    }
}

void RTSPStreamWidget::onShowStreamInfo()
{
    QString info = QString("Stream Information\n\n"
                          "Stream ID: %1\n"
                          "URL: %2\n"
                          "Status: %3\n"
                          "Resolution: %4x%5\n"
                          "FPS: %6\n"
                          "Bitrate: %7 kbps\n"
                          "Frames: %8\n"
                          "Dropped: %9\n"
                          "CPU Usage: %10%\n"
                          "Memory Usage: %11%\n"
                          "Last Update: %12")
                   .arg(m_streamId)
                   .arg(m_streamUrl)
                   .arg(m_stats.status)
                   .arg(m_videoRect.width())
                   .arg(m_videoRect.height())
                   .arg(m_stats.fps)
                   .arg(m_stats.bitrate)
                   .arg(m_stats.frameCount)
                   .arg(m_stats.droppedFrames)
                   .arg(m_stats.cpuUsage, 0, 'f', 1)
                   .arg(m_stats.memoryUsage, 0, 'f', 1)
                   .arg(m_stats.lastUpdate.toString());

    // This would normally show a dialog, but for now just debug output
    qDebug() << "Stream Info:" << info;
}
