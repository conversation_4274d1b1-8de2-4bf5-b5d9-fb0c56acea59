#include "widgets/stream_control_panel.hpp"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QTableWidget>
#include <QHeaderView>
#include <QProgressBar>
#include <QSlider>
#include <QTabWidget>
#include <QTextEdit>
#include <QTimer>
#include <QDateTime>
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>

StreamControlPanel::StreamControlPanel(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_tabWidget(nullptr)
    , m_streamTab(nullptr)
    , m_settingsTab(nullptr)
    , m_monitoringTab(nullptr)
    , m_loggingTab(nullptr)
    , m_addStreamGroup(nullptr)
    , m_urlLineEdit(nullptr)
    , m_usernameLineEdit(nullptr)
    , m_passwordLineEdit(nullptr)
    , m_addButton(nullptr)
    , m_streamListGroup(nullptr)
    , m_streamTable(nullptr)
    , m_removeButton(nullptr)
    , m_startAllButton(nullptr)
    , m_stopAllButton(nullptr)
    , m_refreshButton(nullptr)
    , m_displayGroup(nullptr)
    , m_maxStreamsSpinBox(nullptr)
    , m_showOverlaysCheckBox(nullptr)
    , m_showStatsCheckBox(nullptr)
    , m_autoReconnectCheckBox(nullptr)
    , m_updateTimer(nullptr)
{
    setupUI();
    connectSignals();
    
    // Start update timer
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, [this]() {
        // Update system stats periodically
        // This would normally read real system data
    });
    m_updateTimer->start(2000); // Update every 2 seconds
}

StreamControlPanel::~StreamControlPanel()
{
    if (m_updateTimer) {
        m_updateTimer->stop();
    }
}

void StreamControlPanel::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    
    // Create tab widget
    m_tabWidget = new QTabWidget();
    m_mainLayout->addWidget(m_tabWidget);
    
    setupStreamManagement();
    setupDisplaySettings();
    setupSystemMonitoring();
    setupLogging();
    
    // Add tabs
    m_tabWidget->addTab(m_streamTab, "Streams");
    m_tabWidget->addTab(m_settingsTab, "Settings");
    m_tabWidget->addTab(m_monitoringTab, "Monitor");
    m_tabWidget->addTab(m_loggingTab, "Logs");
}

void StreamControlPanel::setupStreamManagement()
{
    m_streamTab = new QWidget();
    QVBoxLayout* streamLayout = new QVBoxLayout(m_streamTab);
    
    // Add Stream Group
    m_addStreamGroup = new QGroupBox("Add New Stream");
    QGridLayout* addLayout = new QGridLayout(m_addStreamGroup);
    
    addLayout->addWidget(new QLabel("RTSP URL:"), 0, 0);
    m_urlLineEdit = new QLineEdit();
    m_urlLineEdit->setPlaceholderText("rtsp://camera.example.com/stream1");
    addLayout->addWidget(m_urlLineEdit, 0, 1);
    
    addLayout->addWidget(new QLabel("Username:"), 1, 0);
    m_usernameLineEdit = new QLineEdit();
    m_usernameLineEdit->setPlaceholderText("Optional");
    addLayout->addWidget(m_usernameLineEdit, 1, 1);
    
    addLayout->addWidget(new QLabel("Password:"), 2, 0);
    m_passwordLineEdit = new QLineEdit();
    m_passwordLineEdit->setEchoMode(QLineEdit::Password);
    m_passwordLineEdit->setPlaceholderText("Optional");
    addLayout->addWidget(m_passwordLineEdit, 2, 1);
    
    m_addButton = new QPushButton("Add Stream");
    addLayout->addWidget(m_addButton, 3, 0, 1, 2);
    
    streamLayout->addWidget(m_addStreamGroup);
    
    // Stream List Group
    m_streamListGroup = new QGroupBox("Active Streams");
    QVBoxLayout* listLayout = new QVBoxLayout(m_streamListGroup);
    
    // Stream table
    m_streamTable = new QTableWidget(0, 4);
    m_streamTable->setHorizontalHeaderLabels({"ID", "URL", "Status", "Stats"});
    m_streamTable->horizontalHeader()->setStretchLastSection(true);
    m_streamTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_streamTable->setAlternatingRowColors(true);
    listLayout->addWidget(m_streamTable);
    
    // Control buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_removeButton = new QPushButton("Remove");
    m_startAllButton = new QPushButton("Start All");
    m_stopAllButton = new QPushButton("Stop All");
    m_refreshButton = new QPushButton("Refresh");
    
    buttonLayout->addWidget(m_removeButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_startAllButton);
    buttonLayout->addWidget(m_stopAllButton);
    buttonLayout->addWidget(m_refreshButton);
    
    listLayout->addLayout(buttonLayout);
    streamLayout->addWidget(m_streamListGroup);
}

void StreamControlPanel::setupDisplaySettings()
{
    m_settingsTab = new QWidget();
    QVBoxLayout* settingsLayout = new QVBoxLayout(m_settingsTab);
    
    // Display Settings Group
    m_displayGroup = new QGroupBox("Display Settings");
    QGridLayout* displayLayout = new QGridLayout(m_displayGroup);
    
    displayLayout->addWidget(new QLabel("Max Streams per Row:"), 0, 0);
    m_maxStreamsSpinBox = new QSpinBox();
    m_maxStreamsSpinBox->setRange(1, 6);
    m_maxStreamsSpinBox->setValue(3);
    displayLayout->addWidget(m_maxStreamsSpinBox, 0, 1);
    
    m_showOverlaysCheckBox = new QCheckBox("Show Face Overlays");
    m_showOverlaysCheckBox->setChecked(true);
    displayLayout->addWidget(m_showOverlaysCheckBox, 1, 0, 1, 2);
    
    m_showStatsCheckBox = new QCheckBox("Show Statistics");
    m_showStatsCheckBox->setChecked(true);
    displayLayout->addWidget(m_showStatsCheckBox, 2, 0, 1, 2);
    
    m_autoReconnectCheckBox = new QCheckBox("Auto Reconnect");
    m_autoReconnectCheckBox->setChecked(true);
    displayLayout->addWidget(m_autoReconnectCheckBox, 3, 0, 1, 2);
    
    settingsLayout->addWidget(m_displayGroup);
    
    // Quality Settings Group
    m_qualityGroup = new QGroupBox("Quality Settings");
    QGridLayout* qualityLayout = new QGridLayout(m_qualityGroup);
    
    qualityLayout->addWidget(new QLabel("Resolution:"), 0, 0);
    m_resolutionComboBox = new QComboBox();
    m_resolutionComboBox->addItems({"Auto", "1920x1080", "1280x720", "640x480"});
    qualityLayout->addWidget(m_resolutionComboBox, 0, 1);
    
    qualityLayout->addWidget(new QLabel("Quality:"), 1, 0);
    m_qualitySlider = new QSlider(Qt::Horizontal);
    m_qualitySlider->setRange(1, 10);
    m_qualitySlider->setValue(8);
    qualityLayout->addWidget(m_qualitySlider, 1, 1);
    
    m_qualityLabel = new QLabel("High");
    qualityLayout->addWidget(m_qualityLabel, 1, 2);
    
    settingsLayout->addWidget(m_qualityGroup);
    
    // Performance Settings Group
    m_performanceGroup = new QGroupBox("Performance Settings");
    QGridLayout* perfLayout = new QGridLayout(m_performanceGroup);
    
    perfLayout->addWidget(new QLabel("Buffer Size (MB):"), 0, 0);
    m_bufferSizeSpinBox = new QSpinBox();
    m_bufferSizeSpinBox->setRange(1, 100);
    m_bufferSizeSpinBox->setValue(10);
    perfLayout->addWidget(m_bufferSizeSpinBox, 0, 1);
    
    perfLayout->addWidget(new QLabel("Timeout (ms):"), 1, 0);
    m_timeoutSpinBox = new QSpinBox();
    m_timeoutSpinBox->setRange(1000, 30000);
    m_timeoutSpinBox->setValue(5000);
    perfLayout->addWidget(m_timeoutSpinBox, 1, 1);
    
    perfLayout->addWidget(new QLabel("Retry Count:"), 2, 0);
    m_retryCountSpinBox = new QSpinBox();
    m_retryCountSpinBox->setRange(0, 10);
    m_retryCountSpinBox->setValue(3);
    perfLayout->addWidget(m_retryCountSpinBox, 2, 1);
    
    settingsLayout->addWidget(m_performanceGroup);
    settingsLayout->addStretch();
}

void StreamControlPanel::setupSystemMonitoring()
{
    m_monitoringTab = new QWidget();
    QVBoxLayout* monitorLayout = new QVBoxLayout(m_monitoringTab);
    
    // System Stats Group
    m_systemStatsGroup = new QGroupBox("System Statistics");
    QGridLayout* sysLayout = new QGridLayout(m_systemStatsGroup);
    
    // CPU Usage
    sysLayout->addWidget(new QLabel("CPU Usage:"), 0, 0);
    m_cpuLabel = new QLabel("25%");
    sysLayout->addWidget(m_cpuLabel, 0, 1);
    m_cpuProgressBar = new QProgressBar();
    m_cpuProgressBar->setRange(0, 100);
    m_cpuProgressBar->setValue(25);
    sysLayout->addWidget(m_cpuProgressBar, 0, 2);
    
    // Memory Usage
    sysLayout->addWidget(new QLabel("Memory Usage:"), 1, 0);
    m_memoryLabel = new QLabel("45%");
    sysLayout->addWidget(m_memoryLabel, 1, 1);
    m_memoryProgressBar = new QProgressBar();
    m_memoryProgressBar->setRange(0, 100);
    m_memoryProgressBar->setValue(45);
    sysLayout->addWidget(m_memoryProgressBar, 1, 2);
    
    // Network Usage
    sysLayout->addWidget(new QLabel("Network:"), 2, 0);
    m_networkLabel = new QLabel("15 Mbps");
    sysLayout->addWidget(m_networkLabel, 2, 1);
    m_networkProgressBar = new QProgressBar();
    m_networkProgressBar->setRange(0, 100);
    m_networkProgressBar->setValue(30);
    sysLayout->addWidget(m_networkProgressBar, 2, 2);
    
    monitorLayout->addWidget(m_systemStatsGroup);
    
    // Stream Stats Group
    m_streamStatsGroup = new QGroupBox("Stream Statistics");
    QGridLayout* streamStatsLayout = new QGridLayout(m_streamStatsGroup);
    
    streamStatsLayout->addWidget(new QLabel("Total Streams:"), 0, 0);
    m_totalStreamsLabel = new QLabel("0");
    streamStatsLayout->addWidget(m_totalStreamsLabel, 0, 1);
    
    streamStatsLayout->addWidget(new QLabel("Active Streams:"), 1, 0);
    m_activeStreamsLabel = new QLabel("0");
    streamStatsLayout->addWidget(m_activeStreamsLabel, 1, 1);
    
    streamStatsLayout->addWidget(new QLabel("Total FPS:"), 2, 0);
    m_totalFpsLabel = new QLabel("0");
    streamStatsLayout->addWidget(m_totalFpsLabel, 2, 1);
    
    streamStatsLayout->addWidget(new QLabel("Total Bitrate:"), 3, 0);
    m_totalBitrateLabel = new QLabel("0 kbps");
    streamStatsLayout->addWidget(m_totalBitrateLabel, 3, 1);
    
    monitorLayout->addWidget(m_streamStatsGroup);
    monitorLayout->addStretch();
}

void StreamControlPanel::setupLogging()
{
    m_loggingTab = new QWidget();
    QVBoxLayout* logLayout = new QVBoxLayout(m_loggingTab);
    
    // Log controls
    QHBoxLayout* logControlLayout = new QHBoxLayout();
    logControlLayout->addWidget(new QLabel("Log Level:"));
    
    m_logLevelComboBox = new QComboBox();
    m_logLevelComboBox->addItems({"DEBUG", "INFO", "WARNING", "ERROR"});
    m_logLevelComboBox->setCurrentText("INFO");
    logControlLayout->addWidget(m_logLevelComboBox);
    
    logControlLayout->addStretch();
    
    m_exportLogsButton = new QPushButton("Export");
    m_clearLogsButton = new QPushButton("Clear");
    logControlLayout->addWidget(m_exportLogsButton);
    logControlLayout->addWidget(m_clearLogsButton);
    
    logLayout->addLayout(logControlLayout);
    
    // Log text area
    m_logTextEdit = new QTextEdit();
    m_logTextEdit->setReadOnly(true);
    m_logTextEdit->setFont(QFont("Courier", 9));
    logLayout->addWidget(m_logTextEdit);
    
    // Add some initial log entries
    addLogEntry("Application started", "INFO");
    addLogEntry("Control panel initialized", "INFO");
}

void StreamControlPanel::connectSignals()
{
    // Stream management signals
    connect(m_addButton, &QPushButton::clicked, this, &StreamControlPanel::onAddStreamClicked);
    connect(m_removeButton, &QPushButton::clicked, this, &StreamControlPanel::onRemoveStreamClicked);
    connect(m_startAllButton, &QPushButton::clicked, this, &StreamControlPanel::onStartAllClicked);
    connect(m_stopAllButton, &QPushButton::clicked, this, &StreamControlPanel::onStopAllClicked);
    connect(m_refreshButton, &QPushButton::clicked, this, &StreamControlPanel::onRefreshStreams);

    // Stream table selection
    connect(m_streamTable, &QTableWidget::itemSelectionChanged,
            this, &StreamControlPanel::onStreamListSelectionChanged);

    // Settings signals
    connect(m_maxStreamsSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &StreamControlPanel::maxStreamsPerRowChanged);
    connect(m_showOverlaysCheckBox, &QCheckBox::toggled,
            this, &StreamControlPanel::showFaceOverlaysChanged);
    connect(m_showStatsCheckBox, &QCheckBox::toggled,
            this, &StreamControlPanel::showStatisticsChanged);
    connect(m_autoReconnectCheckBox, &QCheckBox::toggled,
            this, &StreamControlPanel::autoReconnectChanged);

    // Quality slider
    connect(m_qualitySlider, &QSlider::valueChanged, this, [this](int value) {
        QStringList qualityLabels = {"Very Low", "Low", "Poor", "Fair", "Good",
                                    "Better", "High", "Very High", "Excellent", "Best"};
        if (value >= 1 && value <= qualityLabels.size()) {
            m_qualityLabel->setText(qualityLabels[value - 1]);
        }
        emit settingsChanged();
    });

    // Logging signals
    connect(m_exportLogsButton, &QPushButton::clicked, this, &StreamControlPanel::onExportLogs);
    connect(m_clearLogsButton, &QPushButton::clicked, this, &StreamControlPanel::onClearLogs);
}

// Public methods
void StreamControlPanel::addStreamToList(int streamId, const QString& url, const QString& status)
{
    int row = m_streamTable->rowCount();
    m_streamTable->insertRow(row);

    m_streamTable->setItem(row, 0, new QTableWidgetItem(QString::number(streamId)));
    m_streamTable->setItem(row, 1, new QTableWidgetItem(url));
    m_streamTable->setItem(row, 2, new QTableWidgetItem(status));
    m_streamTable->setItem(row, 3, new QTableWidgetItem("0 FPS, 0 kbps"));

    // Store mapping
    m_streamUrls[streamId] = url;
    m_streamStatuses[streamId] = status;

    addLogEntry(QString("Added stream %1: %2").arg(streamId).arg(url), "INFO");
}

void StreamControlPanel::removeStreamFromList(int streamId)
{
    for (int row = 0; row < m_streamTable->rowCount(); ++row) {
        QTableWidgetItem* idItem = m_streamTable->item(row, 0);
        if (idItem && idItem->text().toInt() == streamId) {
            m_streamTable->removeRow(row);
            break;
        }
    }

    m_streamUrls.remove(streamId);
    m_streamStatuses.remove(streamId);

    addLogEntry(QString("Removed stream %1").arg(streamId), "INFO");
}

void StreamControlPanel::updateStreamStatus(int streamId, const QString& status)
{
    for (int row = 0; row < m_streamTable->rowCount(); ++row) {
        QTableWidgetItem* idItem = m_streamTable->item(row, 0);
        if (idItem && idItem->text().toInt() == streamId) {
            m_streamTable->setItem(row, 2, new QTableWidgetItem(status));
            break;
        }
    }

    m_streamStatuses[streamId] = status;
}

void StreamControlPanel::updateStreamStats(int streamId, const QString& stats)
{
    for (int row = 0; row < m_streamTable->rowCount(); ++row) {
        QTableWidgetItem* idItem = m_streamTable->item(row, 0);
        if (idItem && idItem->text().toInt() == streamId) {
            m_streamTable->setItem(row, 3, new QTableWidgetItem(stats));
            break;
        }
    }
}

// Settings methods
void StreamControlPanel::setMaxStreamsPerRow(int count)
{
    m_maxStreamsSpinBox->setValue(count);
}

void StreamControlPanel::setShowFaceOverlays(bool show)
{
    m_showOverlaysCheckBox->setChecked(show);
}

void StreamControlPanel::setShowStatistics(bool show)
{
    m_showStatsCheckBox->setChecked(show);
}

void StreamControlPanel::setAutoReconnect(bool enable)
{
    m_autoReconnectCheckBox->setChecked(enable);
}

// Private slot implementations
void StreamControlPanel::onAddStreamClicked()
{
    QString url = m_urlLineEdit->text().trimmed();
    if (url.isEmpty()) {
        QMessageBox::warning(this, "Invalid Input", "Please enter a valid RTSP URL.");
        return;
    }

    QString username = m_usernameLineEdit->text().trimmed();
    QString password = m_passwordLineEdit->text();

    emit addStreamRequested(url, username, password);

    // Clear input fields
    m_urlLineEdit->clear();
    m_usernameLineEdit->clear();
    m_passwordLineEdit->clear();

    addLogEntry(QString("Requested to add stream: %1").arg(url), "INFO");
}

void StreamControlPanel::onRemoveStreamClicked()
{
    int currentRow = m_streamTable->currentRow();
    if (currentRow >= 0) {
        QTableWidgetItem* idItem = m_streamTable->item(currentRow, 0);
        if (idItem) {
            int streamId = idItem->text().toInt();
            emit removeStreamRequested(streamId);
            addLogEntry(QString("Requested to remove stream %1").arg(streamId), "INFO");
        }
    } else {
        QMessageBox::information(this, "No Selection", "Please select a stream to remove.");
    }
}

void StreamControlPanel::onStartAllClicked()
{
    emit startAllStreamsRequested();
    addLogEntry("Requested to start all streams", "INFO");
}

void StreamControlPanel::onStopAllClicked()
{
    emit stopAllStreamsRequested();
    addLogEntry("Requested to stop all streams", "INFO");
}

void StreamControlPanel::onStreamListSelectionChanged()
{
    int currentRow = m_streamTable->currentRow();
    if (currentRow >= 0) {
        QTableWidgetItem* idItem = m_streamTable->item(currentRow, 0);
        if (idItem) {
            int streamId = idItem->text().toInt();
            emit streamSelectionChanged(streamId);
        }
    }
}

void StreamControlPanel::onSettingsChanged()
{
    emit settingsChanged();
    addLogEntry("Settings changed", "DEBUG");
}

void StreamControlPanel::onRefreshStreams()
{
    updateStreamList();
    addLogEntry("Stream list refreshed", "INFO");
}

void StreamControlPanel::onExportLogs()
{
    QString fileName = QFileDialog::getSaveFileName(this, "Export Logs",
                                                   QString("c-aibox-logs-%1.txt")
                                                   .arg(QDateTime::currentDateTime().toString("yyyyMMdd-hhmmss")),
                                                   "Text Files (*.txt)");
    if (!fileName.isEmpty()) {
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            out << m_logTextEdit->toPlainText();
            addLogEntry(QString("Logs exported to: %1").arg(fileName), "INFO");
        } else {
            QMessageBox::warning(this, "Export Failed", "Could not write to file: " + fileName);
        }
    }
}

void StreamControlPanel::onClearLogs()
{
    m_logTextEdit->clear();
    addLogEntry("Logs cleared", "INFO");
}

// Private helper methods
void StreamControlPanel::updateStreamList()
{
    // This would normally refresh the stream list from the actual data source
    // For now, just update the display
    m_totalStreamsLabel->setText(QString::number(m_streamTable->rowCount()));

    int activeCount = 0;
    for (int row = 0; row < m_streamTable->rowCount(); ++row) {
        QTableWidgetItem* statusItem = m_streamTable->item(row, 2);
        if (statusItem && statusItem->text() == "Streaming") {
            activeCount++;
        }
    }
    m_activeStreamsLabel->setText(QString::number(activeCount));
}

void StreamControlPanel::addLogEntry(const QString& message, const QString& level)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    QString logEntry = QString("[%1] %2: %3").arg(timestamp).arg(level).arg(message);

    m_logTextEdit->append(logEntry);

    // Auto-scroll to bottom
    QTextCursor cursor = m_logTextEdit->textCursor();
    cursor.movePosition(QTextCursor::End);
    m_logTextEdit->setTextCursor(cursor);
}
