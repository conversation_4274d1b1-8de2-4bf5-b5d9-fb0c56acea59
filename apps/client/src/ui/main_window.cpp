#include "ui/main_window.hpp"
#include "widgets/rtsp_stream_widget.hpp"
#include "widgets/stream_control_panel.hpp"
#include "widgets/system_stats_widget.hpp"
#include <QApplication>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QAction>
#include <QIcon>
#include <QMessageBox>
#include <QInputDialog>
#include <QFileDialog>
#include <QSettings>
#include <QSplitter>
#include <QTabWidget>
#include <QGridLayout>
#include <QScrollArea>
#include <QGroupBox>
#include <QProgressBar>
#include <QLabel>
#include <QTimer>
#include <QDateTime>
#include <QDebug>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainSplitter(nullptr)
    , m_rightSplitter(nullptr)
    , m_streamAreaWidget(nullptr)
    , m_streamScroll<PERSON>rea(nullptr)
    , m_streamGridLayout(nullptr)
    , m_controlPanel(nullptr)
    , m_statsWidget(nullptr)
    , m_tabWidget(nullptr)
    , m_streamTab(nullptr)
    , m_settingsTab(nullptr)
    , m_logsTab(nullptr)
    , m_nextStreamId(1)
    , m_statsUpdateTimer(nullptr)
    , m_mockDataTimer(nullptr)
    , m_statusLabel(nullptr)
    , m_connectionCountLabel(nullptr)
    , m_fpsLabel(nullptr)
    , m_cpuUsageBar(nullptr)
    , m_memoryUsageBar(nullptr)
    , m_maxStreamsPerRow(3)
    , m_showFaceOverlays(true)
    , m_showStatistics(true)
    , m_autoReconnect(true)
{
    setWindowTitle("C-AIBOX - RTSP Stream Viewer with Face Recognition");
    setMinimumSize(1200, 800);
    resize(1600, 1000);

    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    loadStyleSheet();
    connectSignals();
    startMockDataTimer();

    // Add some demo streams
    QTimer::singleShot(1000, this, [this]() {
        onAddStreamClicked(); // Add first demo stream
        QTimer::singleShot(500, this, [this]() {
            onAddStreamClicked(); // Add second demo stream
        });
    });
}

MainWindow::~MainWindow()
{
    if (m_statsUpdateTimer) {
        m_statsUpdateTimer->stop();
    }
    if (m_mockDataTimer) {
        m_mockDataTimer->stop();
    }
}

void MainWindow::setupUI()
{
    // Create central widget
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    // Create main splitter (horizontal)
    m_mainSplitter = new QSplitter(Qt::Horizontal, m_centralWidget);
    
    // Setup stream area
    setupStreamArea();
    
    // Setup control panel
    setupControlPanel();
    
    // Setup stats panel
    setupStatsPanel();

    // Create right splitter (vertical) for control and stats panels
    m_rightSplitter = new QSplitter(Qt::Vertical, m_mainSplitter);
    m_rightSplitter->addWidget(m_controlPanel);
    m_rightSplitter->addWidget(m_statsWidget);
    m_rightSplitter->setSizes({400, 300});

    // Add widgets to main splitter
    m_mainSplitter->addWidget(m_streamScrollArea);
    m_mainSplitter->addWidget(m_rightSplitter);
    m_mainSplitter->setSizes({1000, 400});

    // Set main layout
    QHBoxLayout* mainLayout = new QHBoxLayout(m_centralWidget);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->addWidget(m_mainSplitter);
}

void MainWindow::setupStreamArea()
{
    // Create stream area widget
    m_streamAreaWidget = new QWidget();
    m_streamGridLayout = new QGridLayout(m_streamAreaWidget);
    m_streamGridLayout->setSpacing(10);
    m_streamGridLayout->setContentsMargins(10, 10, 10, 10);

    // Create scroll area
    m_streamScrollArea = new QScrollArea();
    m_streamScrollArea->setWidget(m_streamAreaWidget);
    m_streamScrollArea->setWidgetResizable(true);
    m_streamScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_streamScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
}

void MainWindow::setupControlPanel()
{
    m_controlPanel = new StreamControlPanel();
    m_controlPanel->setMaximumWidth(400);
    m_controlPanel->setMinimumWidth(350);
}

void MainWindow::setupStatsPanel()
{
    // Create a simple stats widget for now
    QWidget* statsWidget = new QWidget();
    statsWidget->setMaximumHeight(300);
    statsWidget->setMinimumHeight(200);
    
    QVBoxLayout* statsLayout = new QVBoxLayout(statsWidget);
    m_statsWidget = statsWidget;
    
    QGroupBox* systemGroup = new QGroupBox("System Statistics");
    QGridLayout* systemLayout = new QGridLayout(systemGroup);
    
    // CPU usage
    systemLayout->addWidget(new QLabel("CPU Usage:"), 0, 0);
    m_cpuUsageBar = new QProgressBar();
    m_cpuUsageBar->setRange(0, 100);
    m_cpuUsageBar->setValue(25);
    systemLayout->addWidget(m_cpuUsageBar, 0, 1);
    
    // Memory usage
    systemLayout->addWidget(new QLabel("Memory Usage:"), 1, 0);
    m_memoryUsageBar = new QProgressBar();
    m_memoryUsageBar->setRange(0, 100);
    m_memoryUsageBar->setValue(45);
    systemLayout->addWidget(m_memoryUsageBar, 1, 1);
    
    statsLayout->addWidget(systemGroup);
    statsLayout->addStretch();
}

void MainWindow::setupMenuBar()
{
    // File menu
    QMenu* fileMenu = menuBar()->addMenu("&File");
    
    QAction* addStreamAction = fileMenu->addAction("&Add Stream...");
    addStreamAction->setShortcut(QKeySequence::New);
    connect(addStreamAction, &QAction::triggered, this, &MainWindow::onAddStreamClicked);
    
    fileMenu->addSeparator();
    
    QAction* exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    
    // View menu
    QMenu* viewMenu = menuBar()->addMenu("&View");
    
    QAction* fullscreenAction = viewMenu->addAction("&Fullscreen");
    fullscreenAction->setShortcut(QKeySequence::FullScreen);
    connect(fullscreenAction, &QAction::triggered, this, &MainWindow::onToggleFullscreen);
    
    // Tools menu
    QMenu* toolsMenu = menuBar()->addMenu("&Tools");
    
    QAction* settingsAction = toolsMenu->addAction("&Settings...");
    connect(settingsAction, &QAction::triggered, this, &MainWindow::onShowSettings);
}

void MainWindow::setupToolBar()
{
    QToolBar* mainToolBar = addToolBar("Main");
    
    QPushButton* addStreamBtn = new QPushButton("Add Stream");
    addStreamBtn->setProperty("class", "toolbar");
    connect(addStreamBtn, &QPushButton::clicked, this, &MainWindow::onAddStreamClicked);
    mainToolBar->addWidget(addStreamBtn);
    
    mainToolBar->addSeparator();
    
    QPushButton* startAllBtn = new QPushButton("Start All");
    startAllBtn->setProperty("class", "toolbar");
    connect(startAllBtn, &QPushButton::clicked, this, &MainWindow::onStartAllStreamsClicked);
    mainToolBar->addWidget(startAllBtn);
    
    QPushButton* stopAllBtn = new QPushButton("Stop All");
    stopAllBtn->setProperty("class", "toolbar");
    connect(stopAllBtn, &QPushButton::clicked, this, &MainWindow::onStopAllStreamsClicked);
    mainToolBar->addWidget(stopAllBtn);
}

void MainWindow::setupStatusBar()
{
    m_statusLabel = new QLabel("Ready");
    statusBar()->addWidget(m_statusLabel);
    
    statusBar()->addPermanentWidget(new QLabel("Streams:"));
    m_connectionCountLabel = new QLabel("0");
    statusBar()->addPermanentWidget(m_connectionCountLabel);
    
    statusBar()->addPermanentWidget(new QLabel("FPS:"));
    m_fpsLabel = new QLabel("0");
    statusBar()->addPermanentWidget(m_fpsLabel);
}

void MainWindow::loadStyleSheet()
{
    // Load the existing stylesheet
    QFile styleFile(":/styles/main.qss");
    if (styleFile.open(QFile::ReadOnly)) {
        QString styleSheet = QLatin1String(styleFile.readAll());
        setStyleSheet(styleSheet);
    }
}

void MainWindow::connectSignals()
{
    // Connect control panel signals
    connect(m_controlPanel, &StreamControlPanel::addStreamRequested,
            this, [this](const QString& url, const QString& username, const QString& password) {
                Q_UNUSED(username)
                Q_UNUSED(password)
                
                // Create new stream widget
                RTSPStreamWidget* streamWidget = new RTSPStreamWidget(m_nextStreamId, url);
                streamWidget->setShowOverlays(m_showFaceOverlays);
                streamWidget->setShowStatistics(m_showStatistics);
                
                // Add to grid layout
                int row = (m_streamWidgets.size()) / m_maxStreamsPerRow;
                int col = (m_streamWidgets.size()) % m_maxStreamsPerRow;
                m_streamGridLayout->addWidget(streamWidget, row, col);
                
                // Add to list
                m_streamWidgets.append(streamWidget);
                m_nextStreamId++;
                
                // Update control panel
                m_controlPanel->addStreamToList(streamWidget->getStreamId(), url, "Stopped");
                
                // Update status
                m_connectionCountLabel->setText(QString::number(m_streamWidgets.size()));
                m_statusLabel->setText("Stream added: " + url);
                
                // Start the stream
                streamWidget->startStream();
            });
    
    connect(m_controlPanel, &StreamControlPanel::removeStreamRequested,
            this, &MainWindow::onRemoveStreamClicked);
}

void MainWindow::startMockDataTimer()
{
    // Start stats update timer
    m_statsUpdateTimer = new QTimer(this);
    connect(m_statsUpdateTimer, &QTimer::timeout, this, &MainWindow::onUpdateStats);
    m_statsUpdateTimer->start(1000); // Update every second
    
    // Start mock data timer
    m_mockDataTimer = new QTimer(this);
    connect(m_mockDataTimer, &QTimer::timeout, this, [this]() {
        // Update mock data for all streams
        for (RTSPStreamWidget* widget : m_streamWidgets) {
            widget->updateMockFrame();
        }
    });
    m_mockDataTimer->start(33); // ~30 FPS
}

// Slot implementations
void MainWindow::onAddStreamClicked()
{
    bool ok;
    QString url = QInputDialog::getText(this, "Add RTSP Stream",
                                       "Enter RTSP URL:", QLineEdit::Normal,
                                       "rtsp://demo.camera.com/stream1", &ok);
    if (ok && !url.isEmpty()) {
        // Emit signal to control panel to add stream
        emit m_controlPanel->addStreamRequested(url, "", "");
    }
}

void MainWindow::onRemoveStreamClicked()
{
    // Find selected stream and remove it
    for (int i = 0; i < m_streamWidgets.size(); ++i) {
        RTSPStreamWidget* widget = m_streamWidgets[i];
        // For now, remove the last stream
        if (i == m_streamWidgets.size() - 1) {
            m_streamGridLayout->removeWidget(widget);
            m_streamWidgets.removeAt(i);
            m_controlPanel->removeStreamFromList(widget->getStreamId());
            widget->deleteLater();

            m_connectionCountLabel->setText(QString::number(m_streamWidgets.size()));
            m_statusLabel->setText("Stream removed");
            break;
        }
    }
}

void MainWindow::onStartAllStreamsClicked()
{
    for (RTSPStreamWidget* widget : m_streamWidgets) {
        widget->startStream();
    }
    m_statusLabel->setText("All streams started");
}

void MainWindow::onStopAllStreamsClicked()
{
    for (RTSPStreamWidget* widget : m_streamWidgets) {
        widget->stopStream();
    }
    m_statusLabel->setText("All streams stopped");
}

void MainWindow::onStreamSelectionChanged()
{
    // Handle stream selection changes
    m_statusLabel->setText("Stream selection changed");
}

void MainWindow::onUpdateStats()
{
    // Update system statistics
    static int cpuCounter = 0;
    static int memCounter = 0;

    // Simulate CPU usage
    cpuCounter = (cpuCounter + 1) % 100;
    int cpuUsage = 20 + (cpuCounter % 30);
    m_cpuUsageBar->setValue(cpuUsage);

    // Simulate memory usage
    memCounter = (memCounter + 2) % 100;
    int memUsage = 40 + (memCounter % 20);
    m_memoryUsageBar->setValue(memUsage);

    // Update FPS counter
    int totalFps = 0;
    for (RTSPStreamWidget* widget : m_streamWidgets) {
        if (widget->isStreaming()) {
            totalFps += 30; // Mock 30 FPS per stream
        }
    }
    m_fpsLabel->setText(QString::number(totalFps));
}

void MainWindow::onToggleFullscreen()
{
    if (isFullScreen()) {
        showNormal();
    } else {
        showFullScreen();
    }
}

void MainWindow::onShowSettings()
{
    QMessageBox::information(this, "Settings",
                           "Settings dialog will be implemented in future versions.");
}
