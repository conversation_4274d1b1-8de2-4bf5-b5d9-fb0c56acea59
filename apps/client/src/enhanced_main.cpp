#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QSplitter>
#include <QScrollArea>
#include <QTimer>
#include <QSettings>
#include <QMessageBox>
#include <QDebug>
#include <QCloseEvent>
#include <memory>

// Forward declarations
class RTSPVideoWidget;
class ConfigurationDialog;
class StreamManager;

/**
 * @brief Enhanced C-AIBOX Client with Real RTSP Support
 * 
 * Features:
 * - Real RTSP stream playback
 * - Configuration modal for server/stream settings
 * - Auto-grid layout for multiple streams
 * - Standalone operation without server
 * - Face recognition overlay support
 */
class EnhancedMainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit EnhancedMainWindow(QWidget *parent = nullptr);
    ~EnhancedMainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void showConfiguration();
    void addStream();
    void removeStream();
    void onStreamAdded(const QString& url, const QString& name);
    void onStreamRemoved(int streamId);
    void onConfigurationChanged();
    void updateStatusBar();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void loadSettings();
    void saveSettings();
    void updateGridLayout();
    void connectToServer();
    void disconnectFromServer();

    // UI Components
    QWidget* m_centralWidget;
    QScrollArea* m_scrollArea;
    QWidget* m_streamContainer;
    QGridLayout* m_gridLayout;
    
    // Dialogs
    ConfigurationDialog* m_configDialog;
    
    // Core components
    StreamManager* m_streamManager;
    
    // Stream widgets
    QList<RTSPVideoWidget*> m_streamWidgets;
    
    // Settings
    QSettings* m_settings;
    
    // Status
    QLabel* m_statusLabel;
    QLabel* m_streamCountLabel;
    QLabel* m_serverStatusLabel;
    
    // Timers
    QTimer* m_statusUpdateTimer;
    
    // Configuration
    QString m_serverAddress;
    int m_serverPort;
    bool m_autoConnect;
    int m_maxStreamsPerRow;
    bool m_showOverlays;
    
    // State
    bool m_isConnectedToServer;
    int m_nextStreamId;
};

EnhancedMainWindow::EnhancedMainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_scrollArea(nullptr)
    , m_streamContainer(nullptr)
    , m_gridLayout(nullptr)
    , m_configDialog(nullptr)
    , m_streamManager(nullptr)
    , m_settings(nullptr)
    , m_statusLabel(nullptr)
    , m_streamCountLabel(nullptr)
    , m_serverStatusLabel(nullptr)
    , m_statusUpdateTimer(nullptr)
    , m_serverAddress("*************")
    , m_serverPort(8080)
    , m_autoConnect(false)
    , m_maxStreamsPerRow(2)
    , m_showOverlays(true)
    , m_isConnectedToServer(false)
    , m_nextStreamId(1)
{
    setWindowTitle("C-AIBOX - Enhanced RTSP Stream Viewer");
    setMinimumSize(1200, 800);
    resize(1600, 1000);

    // Initialize settings
    m_settings = new QSettings("C-AIBOX", "RTSPClient", this);
    
    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    loadSettings();
    
    // Initialize stream manager
    m_streamManager = new StreamManager(this);
    connect(m_streamManager, &StreamManager::streamAdded, this, &EnhancedMainWindow::onStreamAdded);
    connect(m_streamManager, &StreamManager::streamRemoved, this, &EnhancedMainWindow::onStreamRemoved);
    
    // Setup status update timer
    m_statusUpdateTimer = new QTimer(this);
    connect(m_statusUpdateTimer, &QTimer::timeout, this, &EnhancedMainWindow::updateStatusBar);
    m_statusUpdateTimer->start(2000); // Update every 2 seconds
    
    // Auto-connect to server if enabled
    if (m_autoConnect) {
        QTimer::singleShot(1000, this, &EnhancedMainWindow::connectToServer);
    }
    
    // Add default test stream
    QTimer::singleShot(500, this, [this]() {
        m_streamManager->addStream("rtsp://admin:CMC2024!@192.168.222.169:554/streaming/channels/01", "Test Camera 1");
    });
    
    qDebug() << "Enhanced C-AIBOX Client started successfully";
}

EnhancedMainWindow::~EnhancedMainWindow()
{
    saveSettings();
    
    if (m_statusUpdateTimer) {
        m_statusUpdateTimer->stop();
    }
    
    // Clean up stream widgets
    for (auto* widget : m_streamWidgets) {
        widget->deleteLater();
    }
    m_streamWidgets.clear();
}

void EnhancedMainWindow::setupUI()
{
    // Create central widget
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // Create main layout
    QVBoxLayout* mainLayout = new QVBoxLayout(m_centralWidget);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    
    // Create scroll area for streams
    m_scrollArea = new QScrollArea();
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    
    // Create stream container
    m_streamContainer = new QWidget();
    m_gridLayout = new QGridLayout(m_streamContainer);
    m_gridLayout->setSpacing(10);
    m_gridLayout->setContentsMargins(10, 10, 10, 10);
    
    m_scrollArea->setWidget(m_streamContainer);
    mainLayout->addWidget(m_scrollArea);
}

void EnhancedMainWindow::setupMenuBar()
{
    // File menu
    QMenu* fileMenu = menuBar()->addMenu("&File");
    
    QAction* configAction = fileMenu->addAction("&Configuration...");
    configAction->setShortcut(QKeySequence("Ctrl+,"));
    connect(configAction, &QAction::triggered, this, &EnhancedMainWindow::showConfiguration);
    
    fileMenu->addSeparator();
    
    QAction* exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    
    // Stream menu
    QMenu* streamMenu = menuBar()->addMenu("&Stream");
    
    QAction* addStreamAction = streamMenu->addAction("&Add Stream...");
    addStreamAction->setShortcut(QKeySequence::New);
    connect(addStreamAction, &QAction::triggered, this, &EnhancedMainWindow::addStream);
    
    QAction* removeStreamAction = streamMenu->addAction("&Remove Stream");
    removeStreamAction->setShortcut(QKeySequence::Delete);
    connect(removeStreamAction, &QAction::triggered, this, &EnhancedMainWindow::removeStream);
    
    // View menu
    QMenu* viewMenu = menuBar()->addMenu("&View");
    
    QAction* fullscreenAction = viewMenu->addAction("&Fullscreen");
    fullscreenAction->setShortcut(QKeySequence::FullScreen);
    connect(fullscreenAction, &QAction::triggered, this, [this]() {
        if (isFullScreen()) {
            showNormal();
        } else {
            showFullScreen();
        }
    });
}

void EnhancedMainWindow::setupToolBar()
{
    QToolBar* mainToolBar = addToolBar("Main");
    
    QPushButton* configBtn = new QPushButton("Configuration");
    configBtn->setIcon(style()->standardIcon(QStyle::SP_ComputerIcon));
    connect(configBtn, &QPushButton::clicked, this, &EnhancedMainWindow::showConfiguration);
    mainToolBar->addWidget(configBtn);
    
    mainToolBar->addSeparator();
    
    QPushButton* addStreamBtn = new QPushButton("Add Stream");
    addStreamBtn->setIcon(style()->standardIcon(QStyle::SP_MediaPlay));
    connect(addStreamBtn, &QPushButton::clicked, this, &EnhancedMainWindow::addStream);
    mainToolBar->addWidget(addStreamBtn);
    
    QPushButton* removeStreamBtn = new QPushButton("Remove Stream");
    removeStreamBtn->setIcon(style()->standardIcon(QStyle::SP_MediaStop));
    connect(removeStreamBtn, &QPushButton::clicked, this, &EnhancedMainWindow::removeStream);
    mainToolBar->addWidget(removeStreamBtn);
}

void EnhancedMainWindow::setupStatusBar()
{
    m_statusLabel = new QLabel("Ready");
    statusBar()->addWidget(m_statusLabel);
    
    statusBar()->addPermanentWidget(new QLabel("Streams:"));
    m_streamCountLabel = new QLabel("0");
    statusBar()->addPermanentWidget(m_streamCountLabel);
    
    statusBar()->addPermanentWidget(new QLabel("Server:"));
    m_serverStatusLabel = new QLabel("Disconnected");
    m_serverStatusLabel->setStyleSheet("color: red;");
    statusBar()->addPermanentWidget(m_serverStatusLabel);
}

void EnhancedMainWindow::closeEvent(QCloseEvent *event)
{
    saveSettings();
    event->accept();
}

void EnhancedMainWindow::showConfiguration()
{
    if (!m_configDialog) {
        m_configDialog = new ConfigurationDialog(this);
        connect(m_configDialog, &ConfigurationDialog::configurationChanged,
                this, &EnhancedMainWindow::onConfigurationChanged);
    }

    // Set current configuration
    m_configDialog->setServerAddress(m_serverAddress);
    m_configDialog->setServerPort(m_serverPort);
    m_configDialog->setAutoConnect(m_autoConnect);
    m_configDialog->setMaxStreamsPerRow(m_maxStreamsPerRow);
    m_configDialog->setShowOverlays(m_showOverlays);

    m_configDialog->exec();
}

void EnhancedMainWindow::addStream()
{
    if (!m_configDialog) {
        m_configDialog = new ConfigurationDialog(this);
    }

    m_configDialog->showAddStreamDialog();
}

void EnhancedMainWindow::removeStream()
{
    if (!m_streamWidgets.isEmpty()) {
        // Remove the last stream for now
        auto* lastWidget = m_streamWidgets.last();
        m_streamManager->removeStream(lastWidget->getStreamId());
    }
}

void EnhancedMainWindow::onStreamAdded(const QString& url, const QString& name)
{
    // Create new RTSP video widget
    RTSPVideoWidget* streamWidget = new RTSPVideoWidget(m_nextStreamId, url, name, this);
    streamWidget->setShowOverlays(m_showOverlays);

    // Add to layout
    updateGridLayout();

    // Add to list
    m_streamWidgets.append(streamWidget);
    m_nextStreamId++;

    // Update status
    m_streamCountLabel->setText(QString::number(m_streamWidgets.size()));
    m_statusLabel->setText("Stream added: " + name);

    qDebug() << "Added stream:" << name << "URL:" << url;
}

void EnhancedMainWindow::onStreamRemoved(int streamId)
{
    // Find and remove the widget
    for (int i = 0; i < m_streamWidgets.size(); ++i) {
        if (m_streamWidgets[i]->getStreamId() == streamId) {
            RTSPVideoWidget* widget = m_streamWidgets.takeAt(i);
            m_gridLayout->removeWidget(widget);
            widget->deleteLater();
            break;
        }
    }

    // Update layout
    updateGridLayout();

    // Update status
    m_streamCountLabel->setText(QString::number(m_streamWidgets.size()));
    m_statusLabel->setText("Stream removed");

    qDebug() << "Removed stream ID:" << streamId;
}

void EnhancedMainWindow::onConfigurationChanged()
{
    if (m_configDialog) {
        // Get updated configuration
        m_serverAddress = m_configDialog->getServerAddress();
        m_serverPort = m_configDialog->getServerPort();
        m_autoConnect = m_configDialog->getAutoConnect();
        m_maxStreamsPerRow = m_configDialog->getMaxStreamsPerRow();
        m_showOverlays = m_configDialog->getShowOverlays();

        // Apply changes
        updateGridLayout();

        // Update overlay settings for all streams
        for (auto* widget : m_streamWidgets) {
            widget->setShowOverlays(m_showOverlays);
        }

        // Reconnect to server if needed
        if (m_autoConnect && !m_isConnectedToServer) {
            connectToServer();
        } else if (!m_autoConnect && m_isConnectedToServer) {
            disconnectFromServer();
        }

        saveSettings();
        m_statusLabel->setText("Configuration updated");
    }
}

void EnhancedMainWindow::updateStatusBar()
{
    // Update stream count
    m_streamCountLabel->setText(QString::number(m_streamWidgets.size()));

    // Update server status
    if (m_isConnectedToServer) {
        m_serverStatusLabel->setText("Connected");
        m_serverStatusLabel->setStyleSheet("color: green;");
    } else {
        m_serverStatusLabel->setText("Disconnected");
        m_serverStatusLabel->setStyleSheet("color: red;");
    }
}

void EnhancedMainWindow::loadSettings()
{
    // Load server settings
    m_serverAddress = m_settings->value("server/address", "*************").toString();
    m_serverPort = m_settings->value("server/port", 8080).toInt();
    m_autoConnect = m_settings->value("server/auto_connect", false).toBool();

    // Load display settings
    m_maxStreamsPerRow = m_settings->value("display/max_streams_per_row", 2).toInt();
    m_showOverlays = m_settings->value("display/show_overlays", true).toBool();

    // Load window geometry
    restoreGeometry(m_settings->value("window/geometry").toByteArray());
    restoreState(m_settings->value("window/state").toByteArray());
}

void EnhancedMainWindow::saveSettings()
{
    // Save server settings
    m_settings->setValue("server/address", m_serverAddress);
    m_settings->setValue("server/port", m_serverPort);
    m_settings->setValue("server/auto_connect", m_autoConnect);

    // Save display settings
    m_settings->setValue("display/max_streams_per_row", m_maxStreamsPerRow);
    m_settings->setValue("display/show_overlays", m_showOverlays);

    // Save window geometry
    m_settings->setValue("window/geometry", saveGeometry());
    m_settings->setValue("window/state", saveState());
}

void EnhancedMainWindow::updateGridLayout()
{
    // Clear current layout
    while (QLayoutItem* item = m_gridLayout->takeAt(0)) {
        delete item;
    }

    // Re-add widgets in grid pattern
    for (int i = 0; i < m_streamWidgets.size(); ++i) {
        int row = i / m_maxStreamsPerRow;
        int col = i % m_maxStreamsPerRow;
        m_gridLayout->addWidget(m_streamWidgets[i], row, col);
    }
}

void EnhancedMainWindow::connectToServer()
{
    // TODO: Implement WebSocket connection to server
    m_isConnectedToServer = true;
    m_statusLabel->setText("Connected to server: " + m_serverAddress);
    qDebug() << "Connected to server:" << m_serverAddress << ":" << m_serverPort;
}

void EnhancedMainWindow::disconnectFromServer()
{
    // TODO: Implement WebSocket disconnection
    m_isConnectedToServer = false;
    m_statusLabel->setText("Disconnected from server");
    qDebug() << "Disconnected from server";
}

// Include forward declarations
#include "widgets/rtsp_video_widget.hpp"
#include "widgets/configuration_dialog.hpp"
#include "core/stream_manager.hpp"

int main(int argc, char* argv[])
{
    QApplication app(argc, argv);

    app.setApplicationName("C-AIBOX Enhanced Client");
    app.setApplicationVersion("2.0.0");
    app.setOrganizationName("C-AIBOX");
    app.setOrganizationDomain("c-aibox.com");

    EnhancedMainWindow window;
    window.show();

    return app.exec();
}

#include "enhanced_main.moc"
