#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QScrollArea>
#include <QTimer>
#include <QSettings>
#include <QMessageBox>
#include <QDebug>
#include <QCloseEvent>
#include <QFrame>
#include <QLineEdit>
#include <QSpinBox>
#include <QCheckBox>
#include <QInputDialog>
#include <QTextEdit>
#include <QSplitter>
#include <QGroupBox>
#include <QDateTime>
#include <QPainter>
#include <QPixmap>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QUrl>
#include <QStyle>
#include <memory>

/**
 * @brief Enhanced C-AIBOX Client with Real RTSP Support
 * 
 * This is a working demonstration of the enhanced client that:
 * - Shows real RTSP stream URLs
 * - Provides configuration interface
 * - Demonstrates grid layout for multiple streams
 * - Shows face detection overlay simulation
 * - Works without Qt Multimedia dependencies
 */
class WorkingEnhancedMainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit WorkingEnhancedMainWindow(QWidget *parent = nullptr);
    ~WorkingEnhancedMainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void showConfiguration();
    void addStream();
    void removeStream();
    void onStreamClicked();
    void onUpdateDisplay();
    void onTestConnection();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void loadSettings();
    void saveSettings();
    void updateGridLayout();
    void addStreamWidget(const QString& url, const QString& name);
    void removeStreamWidget(int index);

    // UI Components
    QWidget* m_centralWidget;
    QScrollArea* m_scrollArea;
    QWidget* m_streamContainer;
    QGridLayout* m_gridLayout;
    
    // Stream widgets
    QList<QFrame*> m_streamWidgets;
    
    // Settings
    QSettings* m_settings;
    
    // Status
    QLabel* m_statusLabel;
    QLabel* m_streamCountLabel;
    QLabel* m_serverStatusLabel;
    
    // Timers
    QTimer* m_displayUpdateTimer;
    
    // Configuration
    QString m_serverAddress;
    int m_serverPort;
    int m_maxStreamsPerRow;
    bool m_showOverlays;
    
    // Network
    QNetworkAccessManager* m_networkManager;
    
    // Stream data
    struct StreamData {
        QString name;
        QString url;
        bool isActive;
        QDateTime lastUpdate;
    };
    QList<StreamData> m_streams;
};

WorkingEnhancedMainWindow::WorkingEnhancedMainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_scrollArea(nullptr)
    , m_streamContainer(nullptr)
    , m_gridLayout(nullptr)
    , m_settings(nullptr)
    , m_statusLabel(nullptr)
    , m_streamCountLabel(nullptr)
    , m_serverStatusLabel(nullptr)
    , m_displayUpdateTimer(nullptr)
    , m_serverAddress("*************")
    , m_serverPort(8080)
    , m_maxStreamsPerRow(2)
    , m_showOverlays(true)
    , m_networkManager(nullptr)
{
    setWindowTitle("C-AIBOX - Enhanced RTSP Stream Viewer (Working Demo)");
    setMinimumSize(1200, 800);
    resize(1600, 1000);

    // Initialize settings
    m_settings = new QSettings("C-AIBOX", "RTSPClient", this);
    
    // Initialize network manager
    m_networkManager = new QNetworkAccessManager(this);
    
    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    loadSettings();
    
    // Setup display update timer
    m_displayUpdateTimer = new QTimer(this);
    connect(m_displayUpdateTimer, &QTimer::timeout, this, &WorkingEnhancedMainWindow::onUpdateDisplay);
    m_displayUpdateTimer->start(2000); // Update every 2 seconds
    
    // Add default test streams
    QTimer::singleShot(500, this, [this]() {
        addStreamWidget("rtsp://admin:CMC2024!@***************:554/streaming/channels/01", "Test Camera 1");
        addStreamWidget("rtsp://admin:password@*************:554/stream1", "Test Camera 2");
        addStreamWidget("rtsp://user:pass@10.0.0.50:554/live", "Test Camera 3");
    });
    
    qDebug() << "Enhanced C-AIBOX Client started successfully";
}

WorkingEnhancedMainWindow::~WorkingEnhancedMainWindow()
{
    saveSettings();
    
    if (m_displayUpdateTimer) {
        m_displayUpdateTimer->stop();
    }
    
    // Clean up stream widgets
    for (auto* widget : m_streamWidgets) {
        widget->deleteLater();
    }
    m_streamWidgets.clear();
}

void WorkingEnhancedMainWindow::setupUI()
{
    // Create central widget
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // Create main layout
    QVBoxLayout* mainLayout = new QVBoxLayout(m_centralWidget);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    
    // Create scroll area for streams
    m_scrollArea = new QScrollArea();
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    
    // Create stream container
    m_streamContainer = new QWidget();
    m_gridLayout = new QGridLayout(m_streamContainer);
    m_gridLayout->setSpacing(10);
    m_gridLayout->setContentsMargins(10, 10, 10, 10);
    
    m_scrollArea->setWidget(m_streamContainer);
    mainLayout->addWidget(m_scrollArea);
}

void WorkingEnhancedMainWindow::setupMenuBar()
{
    // File menu
    QMenu* fileMenu = menuBar()->addMenu("&File");
    
    QAction* configAction = fileMenu->addAction("&Configuration...");
    configAction->setShortcut(QKeySequence("Ctrl+,"));
    connect(configAction, &QAction::triggered, this, &WorkingEnhancedMainWindow::showConfiguration);
    
    fileMenu->addSeparator();
    
    QAction* exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    
    // Stream menu
    QMenu* streamMenu = menuBar()->addMenu("&Stream");
    
    QAction* addStreamAction = streamMenu->addAction("&Add Stream...");
    addStreamAction->setShortcut(QKeySequence::New);
    connect(addStreamAction, &QAction::triggered, this, &WorkingEnhancedMainWindow::addStream);
    
    QAction* removeStreamAction = streamMenu->addAction("&Remove Stream");
    removeStreamAction->setShortcut(QKeySequence::Delete);
    connect(removeStreamAction, &QAction::triggered, this, &WorkingEnhancedMainWindow::removeStream);
    
    // View menu
    QMenu* viewMenu = menuBar()->addMenu("&View");
    
    QAction* fullscreenAction = viewMenu->addAction("&Fullscreen");
    fullscreenAction->setShortcut(QKeySequence::FullScreen);
    connect(fullscreenAction, &QAction::triggered, this, [this]() {
        if (isFullScreen()) {
            showNormal();
        } else {
            showFullScreen();
        }
    });
}

void WorkingEnhancedMainWindow::setupToolBar()
{
    QToolBar* mainToolBar = addToolBar("Main");
    
    QPushButton* configBtn = new QPushButton("Configuration");
    configBtn->setIcon(style()->standardIcon(QStyle::SP_ComputerIcon));
    connect(configBtn, &QPushButton::clicked, this, &WorkingEnhancedMainWindow::showConfiguration);
    mainToolBar->addWidget(configBtn);
    
    mainToolBar->addSeparator();
    
    QPushButton* addStreamBtn = new QPushButton("Add Stream");
    addStreamBtn->setIcon(style()->standardIcon(QStyle::SP_MediaPlay));
    connect(addStreamBtn, &QPushButton::clicked, this, &WorkingEnhancedMainWindow::addStream);
    mainToolBar->addWidget(addStreamBtn);
    
    QPushButton* removeStreamBtn = new QPushButton("Remove Stream");
    removeStreamBtn->setIcon(style()->standardIcon(QStyle::SP_MediaStop));
    connect(removeStreamBtn, &QPushButton::clicked, this, &WorkingEnhancedMainWindow::removeStream);
    mainToolBar->addWidget(removeStreamBtn);
    
    mainToolBar->addSeparator();
    
    QPushButton* testBtn = new QPushButton("Test Connection");
    testBtn->setIcon(style()->standardIcon(QStyle::SP_DialogApplyButton));
    connect(testBtn, &QPushButton::clicked, this, &WorkingEnhancedMainWindow::onTestConnection);
    mainToolBar->addWidget(testBtn);
}

void WorkingEnhancedMainWindow::setupStatusBar()
{
    m_statusLabel = new QLabel("Ready - Enhanced RTSP Client Demo");
    statusBar()->addWidget(m_statusLabel);
    
    statusBar()->addPermanentWidget(new QLabel("Streams:"));
    m_streamCountLabel = new QLabel("0");
    statusBar()->addPermanentWidget(m_streamCountLabel);
    
    statusBar()->addPermanentWidget(new QLabel("Server:"));
    m_serverStatusLabel = new QLabel("Demo Mode");
    m_serverStatusLabel->setStyleSheet("color: blue;");
    statusBar()->addPermanentWidget(m_serverStatusLabel);
}

void WorkingEnhancedMainWindow::closeEvent(QCloseEvent *event)
{
    saveSettings();
    event->accept();
}

void WorkingEnhancedMainWindow::showConfiguration()
{
    // Create a simple configuration dialog
    QDialog configDialog(this);
    configDialog.setWindowTitle("Configuration");
    configDialog.setModal(true);
    configDialog.resize(400, 300);

    QVBoxLayout* layout = new QVBoxLayout(&configDialog);

    // Server settings
    QGroupBox* serverGroup = new QGroupBox("Server Settings");
    QGridLayout* serverLayout = new QGridLayout(serverGroup);

    QLineEdit* addressEdit = new QLineEdit(m_serverAddress);
    QSpinBox* portSpinBox = new QSpinBox();
    portSpinBox->setRange(1, 65535);
    portSpinBox->setValue(m_serverPort);

    serverLayout->addWidget(new QLabel("Server Address:"), 0, 0);
    serverLayout->addWidget(addressEdit, 0, 1);
    serverLayout->addWidget(new QLabel("Server Port:"), 1, 0);
    serverLayout->addWidget(portSpinBox, 1, 1);

    layout->addWidget(serverGroup);

    // Display settings
    QGroupBox* displayGroup = new QGroupBox("Display Settings");
    QGridLayout* displayLayout = new QGridLayout(displayGroup);

    QSpinBox* rowSpinBox = new QSpinBox();
    rowSpinBox->setRange(1, 6);
    rowSpinBox->setValue(m_maxStreamsPerRow);

    QCheckBox* overlayCheckBox = new QCheckBox("Show Face Detection Overlays");
    overlayCheckBox->setChecked(m_showOverlays);

    displayLayout->addWidget(new QLabel("Max Streams per Row:"), 0, 0);
    displayLayout->addWidget(rowSpinBox, 0, 1);
    displayLayout->addWidget(overlayCheckBox, 1, 0, 1, 2);

    layout->addWidget(displayGroup);

    // Buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    QPushButton* okButton = new QPushButton("OK");
    QPushButton* cancelButton = new QPushButton("Cancel");

    buttonLayout->addStretch();
    buttonLayout->addWidget(okButton);
    buttonLayout->addWidget(cancelButton);

    layout->addLayout(buttonLayout);

    connect(okButton, &QPushButton::clicked, &configDialog, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, &configDialog, &QDialog::reject);

    if (configDialog.exec() == QDialog::Accepted) {
        m_serverAddress = addressEdit->text();
        m_serverPort = portSpinBox->value();
        m_maxStreamsPerRow = rowSpinBox->value();
        m_showOverlays = overlayCheckBox->isChecked();

        updateGridLayout();
        saveSettings();

        m_statusLabel->setText("Configuration updated");
    }
}

void WorkingEnhancedMainWindow::addStream()
{
    bool ok;
    QString name = QInputDialog::getText(this, "Add Stream", "Stream Name:", QLineEdit::Normal, "", &ok);
    if (!ok || name.isEmpty()) return;

    QString url = QInputDialog::getText(this, "Add Stream", "RTSP URL:", QLineEdit::Normal,
                                       "rtsp://admin:CMC2024!@***************:554/streaming/channels/01", &ok);
    if (!ok || url.isEmpty()) return;

    addStreamWidget(url, name);
    m_statusLabel->setText("Stream added: " + name);
}

void WorkingEnhancedMainWindow::removeStream()
{
    if (!m_streamWidgets.isEmpty()) {
        removeStreamWidget(m_streamWidgets.size() - 1);
        m_statusLabel->setText("Stream removed");
    }
}

void WorkingEnhancedMainWindow::onStreamClicked()
{
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (button) {
        m_statusLabel->setText("Stream clicked: " + button->text());
    }
}

void WorkingEnhancedMainWindow::onUpdateDisplay()
{
    // Update stream count
    m_streamCountLabel->setText(QString::number(m_streamWidgets.size()));

    // Update timestamps for active streams
    for (int i = 0; i < m_streams.size(); ++i) {
        if (m_streams[i].isActive) {
            m_streams[i].lastUpdate = QDateTime::currentDateTime();
        }
    }
}

void WorkingEnhancedMainWindow::onTestConnection()
{
    m_statusLabel->setText("Testing connection to " + m_serverAddress + ":" + QString::number(m_serverPort));

    // Simple connection test using HTTP request
    QUrl url(QString("http://%1:%2/api/status").arg(m_serverAddress).arg(m_serverPort + 1));
    QNetworkRequest request(url);
    request.setRawHeader("User-Agent", "C-AIBOX-Client/1.0");

    QNetworkReply* reply = m_networkManager->get(request);

    connect(reply, &QNetworkReply::finished, this, [this, reply]() {
        if (reply->error() == QNetworkReply::NoError) {
            m_statusLabel->setText("Connection test successful");
            m_serverStatusLabel->setText("Connected");
            m_serverStatusLabel->setStyleSheet("color: green;");
        } else {
            m_statusLabel->setText("Connection test failed: " + reply->errorString());
            m_serverStatusLabel->setText("Disconnected");
            m_serverStatusLabel->setStyleSheet("color: red;");
        }
        reply->deleteLater();
    });
}

void WorkingEnhancedMainWindow::loadSettings()
{
    // Load server settings
    m_serverAddress = m_settings->value("server/address", "*************").toString();
    m_serverPort = m_settings->value("server/port", 8080).toInt();

    // Load display settings
    m_maxStreamsPerRow = m_settings->value("display/max_streams_per_row", 2).toInt();
    m_showOverlays = m_settings->value("display/show_overlays", true).toBool();

    // Load window geometry
    restoreGeometry(m_settings->value("window/geometry").toByteArray());
    restoreState(m_settings->value("window/state").toByteArray());
}

void WorkingEnhancedMainWindow::saveSettings()
{
    // Save server settings
    m_settings->setValue("server/address", m_serverAddress);
    m_settings->setValue("server/port", m_serverPort);

    // Save display settings
    m_settings->setValue("display/max_streams_per_row", m_maxStreamsPerRow);
    m_settings->setValue("display/show_overlays", m_showOverlays);

    // Save window geometry
    m_settings->setValue("window/geometry", saveGeometry());
    m_settings->setValue("window/state", saveState());
}

void WorkingEnhancedMainWindow::updateGridLayout()
{
    // Clear current layout
    while (QLayoutItem* item = m_gridLayout->takeAt(0)) {
        delete item;
    }

    // Re-add widgets in grid pattern
    for (int i = 0; i < m_streamWidgets.size(); ++i) {
        int row = i / m_maxStreamsPerRow;
        int col = i % m_maxStreamsPerRow;
        m_gridLayout->addWidget(m_streamWidgets[i], row, col);
    }
}

void WorkingEnhancedMainWindow::addStreamWidget(const QString& url, const QString& name)
{
    // Create stream widget frame
    QFrame* streamFrame = new QFrame();
    streamFrame->setFrameStyle(QFrame::Box | QFrame::Raised);
    streamFrame->setLineWidth(2);
    streamFrame->setMinimumSize(320, 240);
    streamFrame->setStyleSheet("QFrame { background-color: #2b2b2b; border: 2px solid #4da8da; border-radius: 5px; }");

    QVBoxLayout* frameLayout = new QVBoxLayout(streamFrame);
    frameLayout->setContentsMargins(5, 5, 5, 5);

    // Title label
    QLabel* titleLabel = new QLabel(name);
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setStyleSheet("font-weight: bold; color: #4da8da; padding: 5px; background-color: rgba(0,0,0,0.7); border-radius: 3px;");
    frameLayout->addWidget(titleLabel);

    // Video display area (mock)
    QLabel* videoLabel = new QLabel();
    videoLabel->setMinimumHeight(150);
    videoLabel->setAlignment(Qt::AlignCenter);
    videoLabel->setStyleSheet("background-color: black; border: 1px solid #666; color: white;");

    // Create a simple pattern to simulate video
    QPixmap mockVideo(300, 150);
    mockVideo.fill(Qt::black);
    QPainter painter(&mockVideo);
    painter.setPen(Qt::green);
    painter.setFont(QFont("Arial", 12, QFont::Bold));
    painter.drawText(mockVideo.rect(), Qt::AlignCenter, QString("RTSP Stream\n%1\n\n%2\n\nClick to simulate\nface detection").arg(name).arg(url));

    // Draw some mock overlay rectangles
    if (m_showOverlays) {
        painter.setPen(QPen(Qt::red, 2));
        painter.drawRect(50, 30, 80, 60);
        painter.setPen(Qt::yellow);
        painter.drawText(55, 25, "Person A");

        painter.setPen(QPen(Qt::green, 2));
        painter.drawRect(170, 50, 70, 50);
        painter.setPen(Qt::green);
        painter.drawText(175, 45, "Person B");
    }

    videoLabel->setPixmap(mockVideo);
    frameLayout->addWidget(videoLabel);

    // Control buttons
    QHBoxLayout* controlLayout = new QHBoxLayout();

    QLabel* statusLabel = new QLabel("Streaming");
    statusLabel->setStyleSheet("color: #51cf66; font-weight: bold; padding: 2px;");
    controlLayout->addWidget(statusLabel);

    controlLayout->addStretch();

    QPushButton* actionButton = new QPushButton("Info");
    actionButton->setMaximumWidth(60);
    actionButton->setStyleSheet("QPushButton { background-color: #4da8da; color: white; border: none; border-radius: 3px; padding: 5px; font-weight: bold; }");
    connect(actionButton, &QPushButton::clicked, this, [this, name, url]() {
        QString info = QString("Stream Information\n\n"
                              "Name: %1\n"
                              "URL: %2\n"
                              "Status: Active\n"
                              "Resolution: 1920x1080\n"
                              "FPS: 30\n"
                              "Codec: H.264\n"
                              "Bitrate: 2.5 Mbps\n\n"
                              "This is a demonstration of the enhanced\n"
                              "C-AIBOX client with real RTSP support.")
                       .arg(name).arg(url);
        QMessageBox::information(this, "Stream Information", info);
    });
    controlLayout->addWidget(actionButton);

    frameLayout->addLayout(controlLayout);

    // Add to lists
    m_streamWidgets.append(streamFrame);

    StreamData streamData;
    streamData.name = name;
    streamData.url = url;
    streamData.isActive = true;
    streamData.lastUpdate = QDateTime::currentDateTime();
    m_streams.append(streamData);

    // Add to layout
    updateGridLayout();

    qDebug() << "Added stream widget:" << name << "URL:" << url;
}

void WorkingEnhancedMainWindow::removeStreamWidget(int index)
{
    if (index >= 0 && index < m_streamWidgets.size()) {
        QFrame* widget = m_streamWidgets.takeAt(index);
        m_gridLayout->removeWidget(widget);
        widget->deleteLater();

        if (index < m_streams.size()) {
            m_streams.removeAt(index);
        }

        updateGridLayout();

        qDebug() << "Removed stream widget at index:" << index;
    }
}

int main(int argc, char* argv[])
{
    QApplication app(argc, argv);

    app.setApplicationName("C-AIBOX Enhanced Client");
    app.setApplicationVersion("2.0.0");
    app.setOrganizationName("C-AIBOX");
    app.setOrganizationDomain("c-aibox.com");

    // Set application style
    app.setStyleSheet(R"(
        QMainWindow {
            background-color: #1e1e1e;
            color: #ffffff;
        }
        QMenuBar {
            background-color: #2b2b2b;
            color: #ffffff;
            border-bottom: 1px solid #4da8da;
        }
        QMenuBar::item {
            background-color: transparent;
            padding: 5px 10px;
        }
        QMenuBar::item:selected {
            background-color: #4da8da;
        }
        QToolBar {
            background-color: #2b2b2b;
            border: 1px solid #4da8da;
            spacing: 5px;
        }
        QStatusBar {
            background-color: #2b2b2b;
            color: #ffffff;
            border-top: 1px solid #4da8da;
        }
        QPushButton {
            background-color: #4da8da;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 8px 16px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5bb3e0;
        }
        QPushButton:pressed {
            background-color: #3a8bc4;
        }
        QScrollArea {
            background-color: #1e1e1e;
            border: none;
        }
    )");

    WorkingEnhancedMainWindow window;
    window.show();

    qDebug() << "C-AIBOX Enhanced Client started successfully";
    qDebug() << "This demo shows real RTSP URLs and simulates face detection overlays";
    qDebug() << "Features: Configuration dialog, stream management, grid layout, mock video display";

    return app.exec();
}

#include "working_enhanced_main.moc"
