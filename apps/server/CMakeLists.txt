# Đ<PERSON><PERSON> bảo CMake version hiện đại
cmake_minimum_required(VERSION 3.18)

# Đặt tên project cho app này
project(server)

# Setup cpp-httplib dependency
setup_httplib_dependency()

# Collect all source files
file(GLOB_RECURSE SERVER_SOURCES
    "src/*.cpp"
)

# Thêm source files
add_executable(server
    ${SERVER_SOURCES}
)

# Set target properties
set_target_properties_common(server)

# Include directories
target_include_directories(server
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# Link với các thư viện nội bộ (models, shared...) nếu cần
target_link_libraries(server
    PRIVATE
        models           # libraries/models
        shared           # libraries/shared (for JSON utilities)
        httplib::httplib # cpp-httplib for HTTP server
        # Thêm lib khác nếu cần
)

# Link with OpenSSL if available (required by shared library)
if(USE_OPENSSL)
    target_link_libraries(server PRIVATE OpenSSL::SSL OpenSSL::Crypto)
endif()

# Nếu cần compile flags đặc biệt cho app này
# target_compile_options(server PRIVATE -Wall -Wextra)

# Nếu có resources, assets, cấu hình, copy file vào build dir nếu cần
# file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/config.json DESTINATION ${CMAKE_CURRENT_BINARY_DIR})

# Optional: Cài đặt app này (install)
# install(TARGETS server DESTINATION bin)

# Nếu cần thêm test cho app, add_subdirectory(test) tại đây
