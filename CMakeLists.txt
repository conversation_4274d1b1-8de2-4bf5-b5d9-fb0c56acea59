cmake_minimum_required(VERSION 3.18)
project(c-aibox-v1 VERSION 1.0.0)

# Include CMake utilities
include(cmake/compiler_flags.cmake)
include(cmake/dependencies.cmake)
include(cmake/utils.cmake)

# Set default build type
set_default_build_type()

# Print build information
print_build_info()

# Setup common dependencies
setup_common_dependencies()

# Setup RTSP and multimedia dependencies
setup_rtsp_dependencies()

# Setup Qt5 dependencies for GUI applications
setup_qt5_dependencies()

# Add libraries
add_subdirectory(libraries/models)
add_subdirectory(libraries/shared)
add_subdirectory(libraries/rtsp)

# Add applications
add_subdirectory(apps/client)
add_subdirectory(apps/server)

# Tests are now included in individual library CMakeLists.txt files
# Enable testing globally
option(BUILD_TESTING "Build tests" ON)

# Add examples (optional, can be disabled with -DBUILD_EXAMPLES=OFF)
option(BUILD_EXAMPLES "Build examples" ON)
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()
