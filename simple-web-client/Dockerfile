FROM nginx:alpine

# Copy web files
COPY public /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create user
RUN addgroup -g 1001 aibox && \
    adduser -D -s /bin/bash -u 1001 -G aibox aibox

# Create directories and set permissions
RUN mkdir -p /app/data /app/logs && \
    chown -R aibox:aibox /app && \
    chown -R aibox:aibox /usr/share/nginx/html

EXPOSE 80 3000

CMD ["nginx", "-g", "daemon off;"]
