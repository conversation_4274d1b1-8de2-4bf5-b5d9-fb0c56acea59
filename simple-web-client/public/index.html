<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C-AIBOX Web Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .control-group {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
        }
        .control-group h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            min-height: 100px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 C-AIBOX Web Client</h1>
            <p>Simple Web Interface for C-AIBOX System</p>
        </div>

        <div class="status-panel">
            <h3>System Status</h3>
            <div id="status">Checking connection...</div>
        </div>

        <div class="controls">
            <div class="control-group">
                <h3>Server Operations</h3>
                <button onclick="checkHealth()">Check Health</button>
                <button onclick="getStatus()">Get Status</button>
                <button onclick="getModels()">List Models</button>
            </div>

            <div class="control-group">
                <h3>AI Operations</h3>
                <button onclick="runInference()">Run Inference</button>
                <button onclick="testAPI()">Test All APIs</button>
            </div>
        </div>

        <div class="output" id="output">Ready to test C-AIBOX system...</div>
    </div>

    <script>
        const output = document.getElementById('output');
        const status = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, options);
                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function checkHealth() {
            log('Checking server health...');
            const result = await makeRequest('/health');
            if (result.success) {
                log(`Health check: ${JSON.stringify(result.data)}`, 'success');
                status.innerHTML = `<span class="success">✅ Server is healthy</span>`;
            } else {
                log(`Health check failed: ${result.error}`, 'error');
                status.innerHTML = `<span class="error">❌ Server is not responding</span>`;
            }
        }

        async function getStatus() {
            log('Getting server status...');
            const result = await makeRequest('/api/status');
            if (result.success) {
                log(`Status: ${JSON.stringify(result.data)}`, 'success');
            } else {
                log(`Status check failed: ${result.error}`, 'error');
            }
        }

        async function getModels() {
            log('Fetching available models...');
            const result = await makeRequest('/api/models');
            if (result.success) {
                log(`Models: ${JSON.stringify(result.data)}`, 'success');
            } else {
                log(`Failed to fetch models: ${result.error}`, 'error');
            }
        }

        async function runInference() {
            log('Running inference...');
            const result = await makeRequest('/api/inference', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: 'simple-model-1',
                    input: 'test input data'
                })
            });
            if (result.success) {
                log(`Inference result: ${JSON.stringify(result.data)}`, 'success');
            } else {
                log(`Inference failed: ${result.error}`, 'error');
            }
        }

        async function testAPI() {
            log('Running comprehensive API test...');
            await checkHealth();
            await getStatus();
            await getModels();
            await runInference();
            log('API test completed!', 'success');
        }

        // Initialize
        window.onload = function() {
            checkHealth();
        };
    </script>
</body>
</html>
