const express = require('express');
const app = express();
const port = process.env.SERVER_PORT || 8080;

app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'c-aibox-simple-server' });
});

// Mock API endpoints
app.get('/api/status', (req, res) => {
  res.json({ status: 'running', message: 'Simple C-AIBOX Server' });
});

app.get('/api/models', (req, res) => {
  res.json({ models: ['simple-model-1', 'simple-model-2'] });
});

app.post('/api/inference', (req, res) => {
  res.json({ result: 'Simple inference result', timestamp: new Date().toISOString() });
});

// Static file serving for web client
app.use(express.static('public'));

app.listen(port, '0.0.0.0', () => {
  console.log(`Simple C-AIBOX Server running on port ${port}`);
});
