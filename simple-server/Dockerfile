FROM node:18-alpine

WORKDIR /app

# Copy package.json and install dependencies
COPY package.json .
RUN npm install

# Copy server files
COPY server.js .

# Create user
RUN addgroup -g 1001 aibox && \
    adduser -D -s /bin/bash -u 1001 -G aibox aibox

# Create directories
RUN mkdir -p /app/data /app/logs /app/models && \
    chown -R aibox:aibox /app

USER aibox

EXPOSE 8080

CMD ["node", "server.js"]
