# Hệ Thống Nhận Diện Khuôn Mặt Với Orange Pi - Kiến Trúc Triển Khai Linh Hoạt

## 1. Tổng Quan 🎯

Tài liệu này mô tả kiến trúc triển khai linh hoạt cho hệ thống nhận diện khuôn mặt sử dụng Orange Pi làm server xử lý AI. Hệ thống được thiết kế để hỗ trợ **hai chế độ hoạt động có thể cấu hình** cho việc truyền tải video đến client, nhằm tối ưu hóa tài nguyên và thích ứng với khả năng của camera:
1.  **Chế độ Trực tiếp (Direct Mode)**: Client App nhận luồng video trực tiếp từ camera (khi camera hỗ trợ nhiều luồng đồng thời).
2.  **<PERSON><PERSON> độ Phân phối lại (Re-stream Mode)**: Orange Pi Server nhận luồng video duy nhất từ camera và phân phối lại cho Client App (khi camera hạn chế số luồng hoặc cần tối ưu khác).

Mục tiêu là hiển thị video thời gian thực trên ứng dụng client (là một **Qt Application chạy trên thiết bị nhúng**), đồng thời nhận kết quả nhận diện/xác thực khuôn mặt từ server một cách không đồng bộ. Server cũng có khả năng kích hoạt đồng thời nhiều loại hook/side effect khác sau khi nhận diện thành công.

---

## 2. Kiến Trúc Hệ Thống (Hỗ trợ 2 Chế độ) 🏗️

Hệ thống bao gồm các thành phần chính:

* **Camera IP**: Cung cấp luồng video thời gian thực qua giao thức RTSP.
* **Orange Pi Server (AI Processor & Conditional Video Distributor)**:
    * Kết nối đến luồng RTSP từ camera.
    * Thực hiện các tác vụ AI (phát hiện, crop, xác thực khuôn mặt).
    * **Tùy theo chế độ cấu hình**:
        * **Direct Mode**: Chỉ xử lý AI.
        * **Re-stream Mode**: Vừa xử lý AI, vừa phân phối lại (re-stream) luồng video cho Client Application.
    * Gửi kết quả xử lý AI (ảnh khuôn mặt đã crop và thông tin xác thực) cho Client App qua kênh riêng (WebSocket/MQTT).
    * Kích hoạt đồng thời nhiều hook/side effect khác khi nhận diện thành công.
* **Client Application**:
    * Là một **Qt Application chạy trên thiết bị nhúng (embedded device)**.
    * **Tùy theo chế độ cấu hình**:
        * **Direct Mode**: Nhận luồng video RTSP trực tiếp từ Camera IP.
        * **Re-stream Mode**: Nhận luồng video đã được Orange Pi Server phân phối lại.
    * Nhận và hiển thị kết quả nhận diện từ Orange Pi Server qua kênh riêng.
* **Kênh Giao Tiếp (WebSocket/MQTT)**: Dùng để Orange Pi Server gửi dữ liệu kết quả AI cho Client App (không đổi giữa các chế độ).

### Sơ đồ khối (Kiến trúc linh hoạt):

```mermaid
graph TD
    CAM[Camera IP <br/> (RTSP Stream)]

    subgraph "Orange Pi Server"
        direction LR
        OPI_RTSP_IN[Nhận RTSP từ Camera]
        OPI_AI[Xử lý AI <br/> - Face Detection <br/> - Face Crop <br/> - Face Identification]
        OPI_HOOKS[Trigger Hooks/Side Effects]
        OPI_RESTREAM[Module Re-stream Video <br/> (WebRTC, RTSP, MJPEG) <br/> <i>Chỉ hoạt động ở Re-stream Mode</i>]
        OPI_WS_MQTT_OUT[Gửi kết quả AI <br/> (Ảnh crop + Thông tin) <br/> qua WebSocket/MQTT]
    end

    CLIENTAPP[Client Application <br/> (Qt on Embedded Device) <br/> - Hiển thị Video <br/> - Nhận kết quả AI]
    HOOK_TARGETS{Hooks / Side Effects <br/> (Relay, API, DB, etc.)}

    CAM -->|RTSP| OPI_RTSP_IN
    OPI_RTSP_IN --> OPI_AI
    OPI_AI --> OPI_HOOKS
    OPI_AI --> OPI_WS_MQTT_OUT
    OPI_HOOKS --> HOOK_TARGETS
    OPI_WS_MQTT_OUT --> CLIENTAPP

    OPI_RTSP_IN --> OPI_RESTREAM
    OPI_RESTREAM -.->|Video Re-stream <br/> <i>(Re-stream Mode)</i>| CLIENTAPP
    CAM -.->|RTSP Direct <br/> <i>(Direct Mode)</i>| CLIENTAPP

    linkStyle 7 stroke-dasharray: 5 5;
    linkStyle 8 stroke-dasharray: 5 5;
```

---

## 3. Chi Tiết Luồng Dữ Liệu (Data Flow Details) 🔄

### 3.1 Chế Độ Trực Tiếp (Direct Mode)

```mermaid
sequenceDiagram
    participant CAM as Camera IP
    participant OPI as Orange Pi Server
    participant CLIENT as Client App (Qt)
    participant WS as WebSocket/MQTT

    Note over CAM,CLIENT: Direct Mode - Client nhận video trực tiếp từ camera

    CAM->>OPI: RTSP Stream (AI Processing)
    CAM->>CLIENT: RTSP Stream (Display)

    activate OPI
    OPI->>OPI: Face Detection (NPU)
    OPI->>OPI: Face Crop & Alignment (RGA)
    OPI->>OPI: Face Recognition (NPU)
    OPI->>OPI: Trigger Hooks/Side Effects
    deactivate OPI

    OPI->>WS: Face Detection Results
    WS->>CLIENT: AI Results (Async)

    Note over CLIENT: Client hiển thị video từ camera<br/>+ overlay kết quả AI từ server
```

### 3.2 Chế Độ Phân Phối Lại (Re-stream Mode)

```mermaid
sequenceDiagram
    participant CAM as Camera IP
    participant OPI as Orange Pi Server
    participant CLIENT as Client App (Qt)
    participant WS as WebSocket/MQTT

    Note over CAM,CLIENT: Re-stream Mode - Server phân phối lại video cho client

    CAM->>OPI: RTSP Stream (Single Connection)

    activate OPI
    par AI Processing
        OPI->>OPI: Face Detection (NPU)
        OPI->>OPI: Face Crop & Alignment (RGA)
        OPI->>OPI: Face Recognition (NPU)
        OPI->>OPI: Trigger Hooks/Side Effects
    and Video Re-streaming
        OPI->>OPI: Video Processing (MPP/RGA)
        OPI->>CLIENT: Re-streamed Video (WebRTC/RTSP/MJPEG)
    end
    deactivate OPI

    OPI->>WS: Face Detection Results
    WS->>CLIENT: AI Results (Async)

    Note over CLIENT: Client hiển thị video từ server<br/>+ overlay kết quả AI
```

---

## 4. Kiến Trúc Kỹ Thuật Chi Tiết (Technical Architecture) ⚙️

### 4.1 Orange Pi Server - Kiến Trúc Module

```mermaid
graph TB
    subgraph "Orange Pi Server (RK3588)"
        subgraph "RTSP Input Module"
            RTSP_MGR[RTSP Manager<br/>- Connection Pool<br/>- Stream Multiplexer<br/>- Health Monitor]
            RTSP_RECV[Stream Receivers<br/>- GStreamer Pipeline<br/>- MPP Hardware Decoder<br/>- DMABUF Zero-Copy]
            RTSP_QUEUE[Frame Queues<br/>- Thread-Safe Buffers<br/>- Priority Management<br/>- Flow Control]
        end

        subgraph "AI Processing Pipeline"
            FACE_DET[Face Detection<br/>- RKNN NPU Core 0<br/>- YOLOv8/RetinaFace<br/>- Batch Processing]
            FACE_EXT[Face Extraction<br/>- RGA Hardware Scaler<br/>- Face Alignment<br/>- Quality Assessment]
            FACE_REC[Face Recognition<br/>- RKNN NPU Core 1-2<br/>- ArcFace/FaceNet<br/>- Embedding Generation]
            FACE_MATCH[Face Matching<br/>- Vector Database<br/>- Similarity Search<br/>- Identity Resolution]
        end

        subgraph "Video Re-streaming (Optional)"
            VIDEO_PROC[Video Processor<br/>- Format Conversion<br/>- Quality Adaptation<br/>- Bandwidth Control]
            STREAM_OUT[Stream Output<br/>- WebRTC Server<br/>- RTSP Re-streamer<br/>- MJPEG HTTP]
        end

        subgraph "Communication & Control"
            WS_SERVER[WebSocket Server<br/>- Real-time Results<br/>- Client Communication<br/>- Event Broadcasting]
            API_SERVER[REST API Server<br/>- Configuration<br/>- Stream Management<br/>- Statistics]
            HOOK_MGR[Hook Manager<br/>- Event Triggers<br/>- External Integrations<br/>- Side Effects]
        end
    end

    RTSP_MGR --> RTSP_RECV
    RTSP_RECV --> RTSP_QUEUE
    RTSP_QUEUE --> FACE_DET
    RTSP_QUEUE --> VIDEO_PROC
    FACE_DET --> FACE_EXT
    FACE_EXT --> FACE_REC
    FACE_REC --> FACE_MATCH
    FACE_MATCH --> WS_SERVER
    FACE_MATCH --> HOOK_MGR
    VIDEO_PROC --> STREAM_OUT
```

### 4.2 Client Application - Kiến Trúc Qt

```mermaid
graph TB
    subgraph "Qt Client Application (Embedded Device)"
        subgraph "Video Display Layer"
            VIDEO_WIDGET[Video Display Widget<br/>- RTSP Stream Renderer<br/>- Hardware Acceleration<br/>- Multi-stream Grid]
            OVERLAY_MGR[Overlay Manager<br/>- Face Bounding Boxes<br/>- Recognition Results<br/>- Real-time Updates]
        end

        subgraph "Communication Layer"
            RTSP_CLIENT[RTSP Client<br/>- Direct Camera Connection<br/>- Stream Decoder<br/>- Buffer Management]
            WS_CLIENT[WebSocket Client<br/>- AI Results Receiver<br/>- Real-time Communication<br/>- Event Handling]
            HTTP_CLIENT[HTTP Client<br/>- Configuration API<br/>- Stream Control<br/>- Statistics Query]
        end

        subgraph "UI Control Layer"
            MAIN_WINDOW[Main Window<br/>- Stream Management<br/>- System Monitoring<br/>- Configuration UI]
            CONTROL_PANEL[Control Panel<br/>- Stream Controls<br/>- Settings Management<br/>- Statistics Display]
        end

        subgraph "Data Management"
            CONFIG_MGR[Configuration Manager<br/>- Local Settings<br/>- Stream Profiles<br/>- User Preferences]
            CACHE_MGR[Cache Manager<br/>- Face Recognition Cache<br/>- Stream Metadata<br/>- Performance Data]
        end
    end

    VIDEO_WIDGET --> OVERLAY_MGR
    RTSP_CLIENT --> VIDEO_WIDGET
    WS_CLIENT --> OVERLAY_MGR
    HTTP_CLIENT --> CONTROL_PANEL
    MAIN_WINDOW --> CONTROL_PANEL
    CONFIG_MGR --> MAIN_WINDOW
    CACHE_MGR --> OVERLAY_MGR
```

---

## 5. Tối Ưu Hóa Hiệu Suất (Performance Optimizations) 🚀

### 5.1 Quản Lý Tài Nguyên RK3588

```cpp
// Cấu hình tối ưu cho RK3588
class RK3588ResourceManager {
public:
    void optimizeForFaceRecognition() {
        // CPU Core Assignment
        setCPUAffinity(RTSPThreads, {2, 3});      // A55 cores for I/O
        setCPUAffinity(AIThreads, {4, 5, 6, 7});  // A76 cores for AI

        // NPU Core Allocation
        allocateNPUCore(0, FaceDetection);        // Core 0 for detection
        allocateNPUCore({1, 2}, FaceRecognition); // Core 1-2 for recognition

        // Memory Management
        preallocateDMABUF(64, 1920*1080*3/2);     // 64 buffers for 1080p
        setMemoryQuota(RTSPModule, 2048_MB);      // 2GB for RTSP
        setMemoryQuota(AIModule, 3072_MB);        // 3GB for AI processing

        // Hardware Accelerator Scheduling
        setMaxConcurrentMPP(4);                   // 4 MPP decoder units
        setMaxConcurrentRGA(2);                   // 2 RGA scaler units
    }
};
```

### 5.2 Pipeline Xử Lý Không Đồng Bộ

```cpp
// Zero-copy pipeline với DMABUF
class OptimizedProcessingPipeline {
private:
    ThreadSafeQueue<DMABUFFrame> rtsp_queue_;
    ThreadSafeQueue<DMABUFFrame> detection_queue_;
    ThreadSafeQueue<FaceROI> extraction_queue_;
    ThreadSafeQueue<FaceEmbedding> recognition_queue_;

public:
    void processFrame(DMABUFFrame frame) {
        // Zero-copy frame passing through pipeline
        rtsp_queue_.push(std::move(frame));
    }

    void runDetectionWorker() {
        while (running_) {
            auto frame = rtsp_queue_.pop();
            auto detections = face_detector_.detect(frame);

            for (const auto& detection : detections) {
                FaceROI roi = extractFaceROI(frame, detection);
                extraction_queue_.push(std::move(roi));
            }
        }
    }

    void runRecognitionWorker() {
        std::vector<FaceROI> batch;
        while (running_) {
            // Batch processing for NPU efficiency
            collectBatch(batch, BATCH_SIZE);
            auto embeddings = face_recognizer_.recognize(batch);

            for (auto& embedding : embeddings) {
                recognition_queue_.push(std::move(embedding));
            }
        }
    }
};
```

### 5.3 Chuyển Đổi Chế Độ Thông Minh

```cpp
class IntelligentModeController {
public:
    enum class SystemMode {
        DIRECT_MODE,      // Client kết nối trực tiếp camera
        RESTREAM_MODE,    // Server phân phối lại video
        HYBRID_MODE       // Chuyển đổi động
    };

    SystemMode determineOptimalMode() {
        SystemMetrics metrics = monitor_.getCurrentMetrics();

        // Decision matrix
        if (metrics.active_clients <= 2 &&
            metrics.camera_supports_multiple_streams &&
            metrics.server_cpu_usage > 70%) {
            return SystemMode::DIRECT_MODE;
        }

        if (metrics.active_clients > 3 ||
            !metrics.camera_supports_multiple_streams ||
            metrics.network_quality < 0.8) {
            return SystemMode::RESTREAM_MODE;
        }

        return SystemMode::HYBRID_MODE;
    }

    void switchMode(SystemMode new_mode) {
        switch (new_mode) {
            case SystemMode::DIRECT_MODE:
                disableVideoRestreaming();
                notifyClientsToConnectDirectly();
                break;

            case SystemMode::RESTREAM_MODE:
                enableVideoRestreaming();
                notifyClientsToUseServerStream();
                break;

            case SystemMode::HYBRID_MODE:
                enableAdaptiveRouting();
                break;
        }
    }
};
```

---

## 6. Cấu Hình Hệ Thống (System Configuration) ⚙️

### 6.1 Cấu Hình Orange Pi Server

```json
{
  "system": {
    "mode": "auto",
    "fallback_mode": "restream",
    "max_concurrent_streams": 16,
    "enable_hardware_acceleration": true
  },
  "rtsp_module": {
    "connection_timeout_ms": 5000,
    "retry_count": 3,
    "buffer_size_frames": 30,
    "use_zero_copy": true,
    "enable_statistics": true
  },
  "ai_processing": {
    "face_detection": {
      "model": "yolov8n-face.rknn",
      "confidence_threshold": 0.7,
      "npu_core": 0,
      "batch_size": 4
    },
    "face_recognition": {
      "model": "arcface-mobilenet.rknn",
      "npu_cores": [1, 2],
      "embedding_dimension": 512,
      "batch_size": 8
    }
  },
  "video_restreaming": {
    "enabled": true,
    "format": "webrtc",
    "quality_adaptation": true,
    "max_bitrate_mbps": 8,
    "target_latency_ms": 200
  },
  "communication": {
    "websocket": {
      "port": 8080,
      "max_connections": 50,
      "heartbeat_interval_ms": 30000
    },
    "rest_api": {
      "port": 8081,
      "enable_cors": true,
      "rate_limit_requests_per_minute": 100
    }
  }
}
```

### 6.2 Cấu Hình Client Application

```json
{
  "client": {
    "server_address": "*************",
    "connection_mode": "auto",
    "max_streams_per_view": 4,
    "enable_hardware_decoding": true
  },
  "display": {
    "grid_layout": "2x2",
    "show_face_overlays": true,
    "show_statistics": true,
    "overlay_style": "modern",
    "update_interval_ms": 33
  },
  "communication": {
    "websocket_url": "ws://*************:8080",
    "api_base_url": "http://*************:8081",
    "reconnect_interval_ms": 5000,
    "max_reconnect_attempts": 10
  },
  "performance": {
    "buffer_size_ms": 100,
    "drop_frames_on_delay": true,
    "max_cpu_usage_percent": 80,
    "enable_gpu_acceleration": true
  }
}
```

---

## 7. Kế Hoạch Triển Khai (Implementation Plan) 📋

### 7.1 Lộ Trình Phát Triển

| Giai Đoạn | Thời Gian | Mô Tả | Trạng Thái |
|------------|-----------|--------|------------|
| **Phase 1** | Tuần 1-2 | RTSP Input Module + GUI Skeleton | 🔄 Đang thực hiện |
| **Phase 2** | Tuần 3-4 | Face Detection & Extraction | ⏳ Chờ thực hiện |
| **Phase 3** | Tuần 5-6 | Face Recognition & Matching | ⏳ Chờ thực hiện |
| **Phase 4** | Tuần 7-8 | System Integration & API | 🔄 Một phần hoàn thành |
| **Phase 5** | Tuần 9-10 | Performance Optimization | ⏳ Chờ thực hiện |

### 7.2 Tiến Độ Hiện Tại

```
📊 Tổng Tiến Độ: 20% (3/15 tasks hoàn thành)
🎯 Giai Đoạn Hiện Tại: Phase 1 - Foundation Setup
⏰ Mốc Tiếp Theo: M1 - Foundation Complete (Tuần 1)
🔥 Critical Path: Task 2.1 (RTSP Configuration System)
✅ Thành Tựu Song Song: GUI skeleton hoàn thành độc lập
```

### 7.3 Ưu Tiên Phát Triển

1. **Cao (High Priority)**:
   - ✅ GUI Client Application (Hoàn thành)
   - 🔄 RTSP Input Module (Đang thực hiện)
   - ⏳ Face Detection Integration

2. **Trung Bình (Medium Priority)**:
   - ⏳ Video Re-streaming Module
   - ⏳ WebSocket Communication
   - ⏳ REST API Server

3. **Thấp (Low Priority)**:
   - ⏳ Advanced Analytics
   - ⏳ Performance Monitoring Dashboard
   - ⏳ External Integrations

---

## 8. Tích Hợp Và Kiểm Thử (Integration & Testing) 🧪

### 8.1 Chiến Lược Kiểm Thử

```cpp
// Test Framework cho System Integration
class SystemIntegrationTest {
public:
    void testDirectModeFlow() {
        // 1. Setup mock camera
        MockRTSPCamera camera("rtsp://test.camera/stream");

        // 2. Start Orange Pi Server (AI only)
        OrangePiServer server(ServerMode::AI_ONLY);
        server.start();

        // 3. Start Qt Client (direct connection)
        QtClient client(ConnectionMode::DIRECT);
        client.connectToCamera(camera.getURL());
        client.connectToServer(server.getWebSocketURL());

        // 4. Verify video display
        ASSERT_TRUE(client.isDisplayingVideo());

        // 5. Inject face into camera stream
        camera.injectFace(TestFaces::JOHN_DOE);

        // 6. Verify AI results received
        auto result = client.waitForFaceDetection(5000ms);
        ASSERT_EQ(result.person_name, "John Doe");
        ASSERT_GT(result.confidence, 0.8);
    }

    void testRestreamModeFlow() {
        // Similar test for re-stream mode
        MockRTSPCamera camera("rtsp://test.camera/stream");

        OrangePiServer server(ServerMode::AI_AND_RESTREAM);
        server.start();

        QtClient client(ConnectionMode::RESTREAM);
        client.connectToServer(server.getVideoStreamURL());
        client.connectToServer(server.getWebSocketURL());

        // Test video re-streaming and AI results
        ASSERT_TRUE(client.isDisplayingVideo());

        camera.injectFace(TestFaces::JANE_SMITH);
        auto result = client.waitForFaceDetection(5000ms);
        ASSERT_EQ(result.person_name, "Jane Smith");
    }

    void testModeSwitching() {
        // Test dynamic mode switching
        SystemController controller;

        // Start in direct mode
        controller.setMode(SystemMode::DIRECT_MODE);
        ASSERT_EQ(controller.getCurrentMode(), SystemMode::DIRECT_MODE);

        // Simulate high server load
        controller.simulateHighLoad();

        // Should automatically switch to direct mode
        controller.evaluateAndSwitch();
        ASSERT_EQ(controller.getCurrentMode(), SystemMode::DIRECT_MODE);

        // Simulate multiple clients
        controller.simulateMultipleClients(5);
        controller.evaluateAndSwitch();
        ASSERT_EQ(controller.getCurrentMode(), SystemMode::RESTREAM_MODE);
    }
};
```

### 8.2 Performance Benchmarks

```cpp
class PerformanceBenchmark {
public:
    struct BenchmarkResults {
        float avg_face_detection_time_ms;
        float avg_face_recognition_time_ms;
        float end_to_end_latency_ms;
        float cpu_usage_percent;
        float memory_usage_mb;
        uint32_t max_concurrent_streams;
    };

    BenchmarkResults runFullSystemBenchmark() {
        BenchmarkResults results;

        // Test with increasing number of streams
        for (int streams = 1; streams <= 16; ++streams) {
            auto metrics = benchmarkWithStreams(streams);

            if (metrics.cpu_usage > 90% || metrics.memory_usage > 7000) {
                results.max_concurrent_streams = streams - 1;
                break;
            }

            results = metrics;
        }

        return results;
    }

    // Target Performance Goals
    void validatePerformanceGoals(const BenchmarkResults& results) {
        ASSERT_LT(results.avg_face_detection_time_ms, 50);    // < 50ms detection
        ASSERT_LT(results.avg_face_recognition_time_ms, 100); // < 100ms recognition
        ASSERT_LT(results.end_to_end_latency_ms, 200);        // < 200ms total latency
        ASSERT_LT(results.cpu_usage_percent, 85);             // < 85% CPU usage
        ASSERT_GE(results.max_concurrent_streams, 8);         // >= 8 concurrent streams
    }
};
```

---

## 9. Giám Sát Và Bảo Trì (Monitoring & Maintenance) 📊

### 9.1 Hệ Thống Giám Sát

```cpp
class SystemMonitor {
public:
    struct SystemHealth {
        // Performance Metrics
        float cpu_usage_per_core[8];
        float memory_usage_mb;
        float network_throughput_mbps;
        ThermalState thermal_state;

        // AI Pipeline Metrics
        float avg_detection_fps;
        float avg_recognition_accuracy;
        uint32_t total_faces_processed;
        uint32_t failed_detections;

        // Stream Metrics
        uint32_t active_streams;
        uint32_t total_frames_processed;
        uint32_t dropped_frames;
        float avg_stream_latency_ms;

        // System State
        SystemMode current_mode;
        uint32_t uptime_seconds;
        std::vector<std::string> active_warnings;
    };

    void startMonitoring() {
        monitoring_thread_ = std::thread([this]() {
            while (running_) {
                auto health = collectSystemHealth();

                // Check for issues
                checkPerformanceThresholds(health);
                checkResourceUsage(health);
                checkStreamHealth(health);

                // Log metrics
                logHealthMetrics(health);

                // Send to monitoring dashboard
                sendToMonitoringSystem(health);

                std::this_thread::sleep_for(std::chrono::seconds(5));
            }
        });
    }

private:
    void checkPerformanceThresholds(const SystemHealth& health) {
        if (health.avg_detection_fps < 15) {
            raiseAlert("Low detection FPS", AlertLevel::WARNING);
        }

        if (health.thermal_state == ThermalState::CRITICAL) {
            raiseAlert("Critical temperature", AlertLevel::CRITICAL);
            triggerThermalProtection();
        }

        if (health.memory_usage_mb > 7000) {
            raiseAlert("High memory usage", AlertLevel::WARNING);
            triggerMemoryCleanup();
        }
    }
};
```

### 9.2 Tự Động Hóa Bảo Trì

```cpp
class AutoMaintenanceSystem {
public:
    void scheduleMaintenanceTasks() {
        // Daily tasks
        scheduler_.schedule("0 2 * * *", [this]() {
            performDailyMaintenance();
        });

        // Weekly tasks
        scheduler_.schedule("0 3 * * 0", [this]() {
            performWeeklyMaintenance();
        });

        // Monthly tasks
        scheduler_.schedule("0 4 1 * *", [this]() {
            performMonthlyMaintenance();
        });
    }

private:
    void performDailyMaintenance() {
        // Clear temporary files
        clearTempFiles();

        // Rotate logs
        rotateLogs();

        // Update face database index
        rebuildFaceIndex();

        // Check disk space
        checkDiskSpace();
    }

    void performWeeklyMaintenance() {
        // Defragment face database
        defragmentDatabase();

        // Update AI models if available
        checkForModelUpdates();

        // Generate performance report
        generatePerformanceReport();
    }

    void performMonthlyMaintenance() {
        // Full system backup
        performSystemBackup();

        // Security audit
        performSecurityAudit();

        // Hardware health check
        performHardwareHealthCheck();
    }
};
```

---

## 10. Bảo Mật Và Tuân Thủ (Security & Compliance) 🔒

### 10.1 Bảo Mật Hệ Thống

```cpp
class SecurityManager {
public:
    void initializeSecurity() {
        // Enable TLS for all communications
        enableTLSForWebSocket();
        enableTLSForRESTAPI();
        enableTLSForRTSP();

        // Setup authentication
        setupJWTAuthentication();
        setupRoleBasedAccess();

        // Enable encryption for face data
        enableFaceDataEncryption();

        // Setup audit logging
        enableAuditLogging();
    }

    void validateRTSPConnection(const std::string& rtsp_url) {
        // Validate URL format
        if (!isValidRTSPURL(rtsp_url)) {
            throw SecurityException("Invalid RTSP URL format");
        }

        // Check against whitelist
        if (!isWhitelistedCamera(rtsp_url)) {
            throw SecurityException("Camera not in whitelist");
        }

        // Validate credentials
        if (!validateCameraCredentials(rtsp_url)) {
            throw SecurityException("Invalid camera credentials");
        }
    }

    void encryptFaceData(FaceData& face_data) {
        // Encrypt face embeddings
        face_data.embedding = aes_encrypt(face_data.embedding, encryption_key_);

        // Hash personal information
        face_data.person_id = sha256_hash(face_data.person_id);

        // Add integrity check
        face_data.checksum = calculateChecksum(face_data);
    }
};
```

### 10.2 Tuân Thủ Quy Định

```cpp
class ComplianceManager {
public:
    void ensureGDPRCompliance() {
        // Data minimization
        setDataRetentionPolicy(30); // 30 days

        // Right to be forgotten
        enableDataDeletionAPI();

        // Data portability
        enableDataExportAPI();

        // Consent management
        enableConsentTracking();
    }

    void ensureDataPrivacy() {
        // Anonymize face data
        enableFaceDataAnonymization();

        // Secure data transmission
        enforceEndToEndEncryption();

        // Access logging
        enableDataAccessLogging();

        // Regular security audits
        scheduleSecurityAudits();
    }

    void generateComplianceReport() {
        ComplianceReport report;

        report.data_retention_status = checkDataRetentionCompliance();
        report.encryption_status = checkEncryptionCompliance();
        report.access_control_status = checkAccessControlCompliance();
        report.audit_trail_status = checkAuditTrailCompliance();

        saveComplianceReport(report);
        notifyComplianceOfficer(report);
    }
};
```

---

## 11. Kết Luận Và Hướng Phát Triển (Conclusion & Future Development) 🚀

### 11.1 Tóm Tắt Kiến Trúc

Hệ thống nhận diện khuôn mặt với Orange Pi được thiết kế với kiến trúc linh hoạt, hỗ trợ hai chế độ hoạt động:

- **✅ Chế độ Trực tiếp**: Tối ưu cho ít client, giảm tải server
- **✅ Chế độ Phân phối lại**: Tối ưu cho nhiều client, tập trung xử lý
- **✅ Chuyển đổi thông minh**: Tự động thích ứng với điều kiện hệ thống

### 11.2 Lợi Ích Chính

1. **Hiệu Suất Cao**: Tận dụng tối đa RK3588 NPU và hardware acceleration
2. **Linh Hoạt**: Thích ứng với nhiều kịch bản triển khai khác nhau
3. **Khả Năng Mở Rộng**: Hỗ trợ đến 16 luồng camera đồng thời
4. **Độ Tin Cậy**: Cơ chế retry, failover và monitoring tự động
5. **Bảo Mật**: Mã hóa end-to-end và tuân thủ quy định bảo mật

### 11.3 Hướng Phát Triển Tương Lai

```mermaid
roadmap
    title Lộ Trình Phát Triển Tương Lai

    section Q1 2024
        Core System Implementation    : done, core, 2024-01-01, 2024-03-31
        Basic Face Recognition       : done, face, 2024-02-01, 2024-03-15
        GUI Client Application       : done, gui, 2024-01-15, 2024-02-28

    section Q2 2024
        Advanced AI Features         : active, ai-adv, 2024-04-01, 2024-06-30
        Multi-Camera Support         : active, multi-cam, 2024-04-15, 2024-06-15
        Performance Optimization     : perf, 2024-05-01, 2024-06-30

    section Q3 2024
        Edge AI Enhancements         : edge, 2024-07-01, 2024-09-30
        Cloud Integration           : cloud, 2024-07-15, 2024-09-15
        Mobile App Development      : mobile, 2024-08-01, 2024-09-30

    section Q4 2024
        Enterprise Features         : enterprise, 2024-10-01, 2024-12-31
        Advanced Analytics          : analytics, 2024-10-15, 2024-12-15
        International Deployment    : intl, 2024-11-01, 2024-12-31
```

### 11.4 Khuyến Nghị Triển Khai

1. **Bắt đầu với Chế độ Trực tiếp** cho các triển khai nhỏ (1-2 camera)
2. **Chuyển sang Chế độ Phân phối lại** khi mở rộng (3+ camera hoặc nhiều client)
3. **Sử dụng Chế độ Tự động** cho các hệ thống production
4. **Giám sát liên tục** hiệu suất và tài nguyên hệ thống
5. **Cập nhật thường xuyên** AI models và security patches

---

**📝 Tài liệu này sẽ được cập nhật thường xuyên theo tiến độ phát triển dự án.**