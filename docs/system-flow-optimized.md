# Hệ Thống Nhận Diện Khuôn Mặt Với Orange Pi - Kiến Trúc Triển Khai Linh Hoạt

## 1. Tổng Quan 🎯

Tài liệu này mô tả kiến trúc triển khai linh hoạt cho hệ thống nhận diện khuôn mặt sử dụng Orange Pi làm server xử lý AI. Hệ thống được thiết kế để hỗ trợ **hai chế độ hoạt động có thể cấu hình** cho việc truyền tải video đến client, nhằm tối ưu hóa tài nguyên và thích ứng với khả năng của camera:
1.  **Chế độ Trực tiếp (Direct Mode)**: Client App nhận luồng video trực tiếp từ camera (khi camera hỗ trợ nhiều luồng đồng thời).
2.  **<PERSON><PERSON> độ Phân phối lại (Re-stream Mode)**: Orange Pi Server nhận luồng video duy nhất từ camera và phân phối lại cho Client App (khi camera hạn chế số luồng hoặc cần tối ưu khác).

Mục tiêu là hiển thị video thời gian thực trên ứng dụng client (là một **Qt Application chạy trên thiết bị nhúng**), đồng thời nhận kết quả nhận diện/xác thực khuôn mặt từ server một cách không đồng bộ. Server cũng có khả năng kích hoạt đồng thời nhiều loại hook/side effect khác sau khi nhận diện thành công.

---

## 2. Kiến Trúc Hệ Thống (Hỗ trợ 2 Chế độ) 🏗️

Hệ thống bao gồm các thành phần chính:

* **Camera IP**: Cung cấp luồng video thời gian thực qua giao thức RTSP.
* **Orange Pi Server (AI Processor & Conditional Video Distributor)**:
    * Kết nối đến luồng RTSP từ camera.
    * Thực hiện các tác vụ AI (phát hiện, crop, xác thực khuôn mặt).
    * **Tùy theo chế độ cấu hình**:
        * **Direct Mode**: Chỉ xử lý AI.
        * **Re-stream Mode**: Vừa xử lý AI, vừa phân phối lại (re-stream) luồng video cho Client Application.
    * Gửi kết quả xử lý AI (ảnh khuôn mặt đã crop và thông tin xác thực) cho Client App qua kênh riêng (WebSocket/MQTT).
    * Kích hoạt đồng thời nhiều hook/side effect khác khi nhận diện thành công.
* **Client Application**:
    * Là một **Qt Application chạy trên thiết bị nhúng (embedded device)**.
    * **Tùy theo chế độ cấu hình**:
        * **Direct Mode**: Nhận luồng video RTSP trực tiếp từ Camera IP.
        * **Re-stream Mode**: Nhận luồng video đã được Orange Pi Server phân phối lại.
    * Nhận và hiển thị kết quả nhận diện từ Orange Pi Server qua kênh riêng.
* **Kênh Giao Tiếp (WebSocket/MQTT)**: Dùng để Orange Pi Server gửi dữ liệu kết quả AI cho Client App (không đổi giữa các chế độ).

### Sơ đồ khối (Kiến trúc linh hoạt):

```mermaid
graph TD
    CAM[Camera IP <br/> (RTSP Stream)]

    subgraph "Orange Pi Server"
        direction LR
        OPI_RTSP_IN[Nhận RTSP từ Camera]
        OPI_AI[Xử lý AI <br/> - Face Detection <br/> - Face Crop <br/> - Face Identification]
        OPI_HOOKS[Trigger Hooks/Side Effects]
        OPI_RESTREAM[Module Re-stream Video <br/> (WebRTC, RTSP, MJPEG) <br/> <i>Chỉ hoạt động ở Re-stream Mode</i>]
        OPI_WS_MQTT_OUT[Gửi kết quả AI <br/> (Ảnh crop + Thông tin) <br/> qua WebSocket/MQTT]
    end

    CLIENTAPP[Client Application <br/> (Qt on Embedded Device) <br/> - Hiển thị Video <br/> - Nhận kết quả AI]
    HOOK_TARGETS{Hooks / Side Effects <br/> (Relay, API, DB, etc.)}

    CAM -->|RTSP| OPI_RTSP_IN
    OPI_RTSP_IN --> OPI_AI
    OPI_AI --> OPI_HOOKS
    OPI_AI --> OPI_WS_MQTT_OUT
    OPI_HOOKS --> HOOK_TARGETS
    OPI_WS_MQTT_OUT --> CLIENTAPP

    OPI_RTSP_IN --> OPI_RESTREAM
    OPI_RESTREAM -.->|Video Re-stream <br/> <i>(Re-stream Mode)</i>| CLIENTAPP
    CAM -.->|RTSP Direct <br/> <i>(Direct Mode)</i>| CLIENTAPP

    linkStyle 7 stroke-dasharray: 5 5;
    linkStyle 8 stroke-dasharray: 5 5;