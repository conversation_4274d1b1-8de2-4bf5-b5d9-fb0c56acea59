# C-AIBOX Client GUI Deployment Guide

This guide covers deploying the C-AIBOX client GUI components with Docker containers and environment-based configuration.

## Overview

The C-AIBOX project supports multiple client GUI deployment options:

1. **Qt5 Desktop Client** - Traditional desktop application with VNC access
2. **Web-based Client** - Modern web interface (placeholder for development)
3. **Combined Deployment** - Both clients in flexible configurations

## Prerequisites

- Orange Pi 5 Plus with SSH access
- Docker and Docker Compose installed on Orange Pi
- Cross-compiled binaries in `build-container/bin/`
- Configured `.env` file

## Quick Start

### 1. Configure Environment

Copy the environment template and customize:

```bash
cp .env.template .env
# Edit .env with your Orange Pi IP and settings
```

### 2. Deploy Combined Stack (Recommended)

Deploy server + Qt5 client in a single container:

```bash
./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile combined
```

### 3. Access the GUI

- **VNC Access**: Connect to `***************:5900` (password: `c-aibox123`)
- **Web Placeholder**: Visit `http://***************:8081`
- **API Server**: Visit `http://***************:8080`

## Deployment Options

### Combined Deployment

Single container with server and Qt5 client:

```bash
# Deploy combined stack
./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile combined

# Access via VNC: ***************:5900
# Access API: http://***************:8080
```

### Separate Containers

Deploy server and client in separate containers:

```bash
# Qt5 client
./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile separate --client-type qt5

# Web client
./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile separate --client-type web

# Both clients
./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile separate --client-type both
```

### Client-Only Deployment

Deploy only the client GUI:

```bash
# Qt5 client only
./scripts/deploy/deploy-client-gui.sh --ip *************** --type qt5

# Web client only
./scripts/deploy/deploy-client-gui.sh --ip *************** --type web

# Both clients
./scripts/deploy/deploy-client-gui.sh --ip *************** --type both
```

### Web Client Only

Deploy only the web-based client:

```bash
./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile web-client
```

## Environment Configuration

### Key Environment Variables

```bash
# Client Type Selection
CLIENT_TYPE=qt5  # qt5, web, both

# Display Settings
DISPLAY_MODE=gui
DISPLAY_WIDTH=1920
DISPLAY_HEIGHT=1080

# VNC Settings
VNC_ENABLED=true
VNC_PASSWORD=c-aibox123
VNC_GEOMETRY=1920x1080

# Web Client Settings
WEB_CLIENT_ENABLED=true
WEB_CLIENT_PORT=3000
WEB_CLIENT_NGINX_PORT=8081

# Port Mappings
CLIENT_VNC_PORT=5900
CLIENT_WEB_PORT=3000
CLIENT_WEB_NGINX_PORT=8081
```

### Docker Compose Profiles

- `combined` - Server + Qt5 client in single container
- `separate` - Separate server and Qt5 client containers
- `web-client` - Web client only
- `separate-web` - Separate server and web client containers

## Access Methods

### VNC Access (Qt5 Client)

1. Install VNC viewer on your computer
2. Connect to `ORANGE_PI_IP:5900`
3. Enter password: `c-aibox123` (or your configured password)
4. Access the Qt5 desktop application

### Web Access

1. **Web Client**: `http://ORANGE_PI_IP:3000`
2. **Web Placeholder**: `http://ORANGE_PI_IP:8081`
3. **API Server**: `http://ORANGE_PI_IP:8080`

## Development Workflow

### For Web Client Development

1. Deploy web client container:
   ```bash
   ./scripts/deploy/deploy-client-gui.sh --ip *************** --type web
   ```

2. Access development interface:
   - Direct: `http://***************:3000`
   - Nginx: `http://***************:8081`

3. Develop web interface in `apps/client/web/` directory

4. Rebuild and redeploy:
   ```bash
   ./scripts/deploy/deploy-client-gui.sh --ip *************** --type web
   ```

### For Qt5 Client Development

1. Deploy Qt5 client:
   ```bash
   ./scripts/deploy/deploy-client-gui.sh --ip *************** --type qt5
   ```

2. Access via VNC for testing
3. Modify Qt5 code in `apps/client/src/`
4. Cross-compile and redeploy

## Troubleshooting

### VNC Connection Issues

```bash
# Check VNC service
ssh orangepi@*************** 'docker logs c-aibox-client'

# Restart VNC
ssh orangepi@*************** 'cd c-aibox-deploy && docker compose restart c-aibox-client'
```

### Web Client Issues

```bash
# Check web client logs
ssh orangepi@*************** 'docker logs c-aibox-web-client'

# Check nginx status
ssh orangepi@*************** 'docker exec c-aibox-web-client nginx -t'
```

### Container Management

```bash
# View running containers
ssh orangepi@*************** 'cd c-aibox-deploy && docker compose ps'

# View logs
ssh orangepi@*************** 'cd c-aibox-deploy && docker compose logs'

# Restart services
ssh orangepi@*************** 'cd c-aibox-deploy && docker compose restart'

# Stop services
ssh orangepi@*************** 'cd c-aibox-deploy && docker compose down'
```

## Advanced Configuration

### Custom VNC Settings

```bash
# In .env file
VNC_GEOMETRY=1920x1080
VNC_DEPTH=24
VNC_PASSWORD=your_secure_password
```

### Custom Web Client Settings

```bash
# In .env file
WEB_CLIENT_PORT=3000
WEB_CLIENT_HOST=0.0.0.0
WEB_CLIENT_DEV_MODE=true
```

### Multiple Client Types

Deploy both Qt5 and web clients simultaneously:

```bash
./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile separate --client-type both
```

Access:
- Qt5 via VNC: `***************:5900`
- Web client: `http://***************:3000`
- Web placeholder: `http://***************:8081`

## Security Considerations

1. **VNC Password**: Change default VNC password in `.env`
2. **Network Access**: Configure firewall rules for ports 5900, 3000, 8081
3. **SSH Keys**: Use SSH keys instead of passwords for deployment
4. **Container Security**: Run containers with non-root users

## Next Steps

1. **Web Client Development**: Implement camera UI and AI model controls
2. **API Integration**: Connect web client to server API
3. **Real-time Features**: Add WebSocket support for live camera feeds
4. **Mobile Support**: Optimize web client for mobile devices
5. **Authentication**: Add user authentication and authorization
