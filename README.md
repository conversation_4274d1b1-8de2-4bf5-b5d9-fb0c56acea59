# C-AIBOX: C++ Development Framework for AI Applications

A comprehensive C++ development framework designed for building AI-powered applications with a focus on computer vision and facial recognition systems. This project provides a solid foundation with utility libraries, Qt5 GUI framework, and extensible architecture for AI model integration.

## 🎯 Project Overview

C-AIBOX is a modular C++ framework that provides essential building blocks for developing AI applications. Currently implemented as a development foundation with comprehensive utility libraries, Qt5-based GUI components, and a structured architecture ready for AI model integration.

### Current Implementation Status

- **✅ Comprehensive Utility Libraries**: String, DateTime, File, JSON, Number, Crypto utilities
- **✅ Qt5 GUI Framework**: Professional desktop application with modern UI components
- **✅ Modular Architecture**: Well-structured CMake build system with proper separation of concerns
- **✅ Testing Framework**: GoogleTest integration with comprehensive test suites
- **✅ Cross-Platform Build**: Works on Windows, Linux, and macOS
- **🚧 AI Model Integration**: Basic framework ready for RKNN/OpenCV integration
- **🚧 RTSP Processing**: Architecture prepared for video stream handling
- **🚧 Hardware Acceleration**: Framework designed for NPU integration

## 🏗️ Current Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Qt5 Client    │───▶│  Utility Libs    │───▶│  Model Stubs    │
│   Application   │    │  (String, File,  │    │  (ArcFace,      │
│                 │    │   JSON, etc.)    │    │   YOLO)         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Server Demo    │───▶│  Shared Library  │───▶│  Test Suites    │
│  Application    │    │  (Constants,     │    │  (GoogleTest)   │
│                 │    │   Structs)       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                       ┌──────────────────┐
                       │  Example Demos   │
                       │  (Utility Usage) │
                       └──────────────────┘
```

### Planned AI Pipeline (Future Implementation)
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   RTSP Camera   │───▶│   Stream Decoder │───▶│  Preprocessor   │
│     Streams     │    │     (FFmpeg)     │    │   (OpenCV)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Result Handler │◀───│   Face Matcher   │◀───│ Face Detector   │
│                 │    │     (CPU)        │    │ (RKNN NPU)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                       ┌──────────────────┐    ┌─────────────────┐
                       │ Feature Extractor│◀───│  ROI Extractor  │
                       │   (RKNN NPU)     │    │   (OpenCV)      │
                       └──────────────────┘    └─────────────────┘
```

## 📁 Project Structure

```
c-aibox/
├── apps/                          # Applications
│   ├── client/                   # Qt5 GUI Application
│   │   ├── src/                 # Source code
│   │   │   ├── main.cpp         # Application entry point
│   │   │   ├── core/            # Core application logic
│   │   │   ├── ui/              # User interface components
│   │   │   └── widgets/         # Custom Qt widgets
│   │   ├── include/             # Header files
│   │   ├── resources/           # Qt resources (icons, styles)
│   │   └── CMakeLists.txt       # Build configuration
│   └── server/                   # Demo Server Application
│       ├── src/main.cpp         # Simple model demo
│       └── CMakeLists.txt       # Build configuration
├── libraries/                     # Core Libraries
│   ├── models/                   # AI Model Framework
│   │   ├── include/             # Model interfaces
│   │   │   ├── arcface/         # Face recognition (stub)
│   │   │   └── yolo/            # Face detection (stub)
│   │   ├── src/                 # Model implementations
│   │   └── test/                # Model unit tests
│   ├── shared/                   # Utility Libraries ✅
│   │   ├── include/utils/       # Utility headers
│   │   │   ├── string_utils.hpp # String manipulation
│   │   │   ├── datetime_utils.hpp # Date/time operations
│   │   │   ├── file_utils.hpp   # File operations
│   │   │   ├── json_utils.hpp   # JSON parsing/generation
│   │   │   ├── number_utils.hpp # Number utilities
│   │   │   ├── crypto_utils.hpp # Cryptographic functions
│   │   │   └── regex_utils.hpp  # Regular expressions
│   │   ├── src/                 # Implementation files
│   │   └── test/                # Comprehensive test suites
│   └── stream/                   # Stream processing (planned)
├── examples/                      # Working Examples ✅
│   ├── string_utils_demo.cpp    # String utilities demo
│   ├── datetime_utils_demo.cpp  # DateTime utilities demo
│   ├── file_utils_demo.cpp      # File utilities demo
│   ├── json_utils_demo.cpp      # JSON utilities demo
│   └── number_utils_demo.cpp    # Number utilities demo
├── docs/                         # Documentation
│   ├── deep-research/           # Technical research documents
│   ├── tasks-defined/           # Task breakdown and planning
│   ├── project-structure-guideline.md
│   ├── coding-convention-naming-guideline.md
│   └── setup-development-guideline.md
├── cmake/                        # CMake Configuration
│   ├── compiler_flags.cmake    # Compiler settings
│   ├── dependencies.cmake      # Dependency management
│   └── utils.cmake             # Build utilities
├── scripts/                      # Build Scripts ✅
│   ├── build.sh                # Cross-platform build script
│   ├── test.sh                 # Test execution script
│   ├── format.sh               # Code formatting
│   └── clean.sh                # Cleanup script
└── build/                        # Build Output (generated)
    ├── bin/examples/            # Example executables
    ├── apps/client/client_app   # Qt5 GUI application
    ├── bin/server               # HTTP API server
    └── lib/                     # Static libraries
```

## 🔧 Technical Requirements

### Current Requirements (Implemented)
- **CMake 3.18+** for build system
- **C++17 compatible compiler** (GCC 8+, Clang 8+, MSVC 2019+)
- **Qt5** for GUI application (Core, Widgets, GUI, Network, Multimedia)
- **cpp-httplib** for HTTP server (automatically downloaded)
- **GoogleTest** for testing framework (automatically downloaded)

### Future Requirements (Planned)
- **OpenCV 4.0+** for computer vision operations
- **FFmpeg/GStreamer** for video stream processing
- **RKNN Runtime 2.0+** for NPU acceleration (RK3588 hardware)
- **Docker** for containerized deployment

### Platform Support
- **Windows** ✅ (MSVC, MinGW)
- **Linux** ✅ (GCC, Clang)
- **macOS** ✅ (Clang, Apple Silicon)

## 🚀 Quick Start

### Prerequisites Setup

This project uses VSCode Dev Container for development, which provides a consistent, pre-configured environment:

```bash
# 1. Install prerequisites on your host machine
# - Docker Desktop
# - Visual Studio Code
# - Dev Containers extension for VSCode

# 2. Clone the repository
git clone <repository-url>
cd c-aibox

# 3. Open in VSCode
code .

# 4. When prompted, click "Reopen in Container"
# Or use Command Palette: "Dev Containers: Reopen in Container"

# 5. The container will automatically install:
# - Ubuntu 22.04 base image
# - CMake 3.18+
# - GCC 11+ with C++17 support
# - Qt5 development packages
# - GoogleTest framework
# - All build tools and dependencies
```

**Dev Container Features:**
- ✅ **Zero Setup** - All dependencies pre-installed
- ✅ **Consistent Environment** - Same setup across all developers
- ✅ **Isolated** - No conflicts with host system
- ✅ **VS Code Integration** - IntelliSense, debugging, extensions
- ✅ **Port Forwarding** - Access GUI applications

### Building the Project

After opening the project in the dev container, building is straightforward:

#### Using Build Script (Recommended)
```bash
# Build in Debug mode (default)
./scripts/build.sh

# Build in Release mode
./scripts/build.sh Release

# Clean build
./scripts/build.sh clean

# All dependencies are pre-installed, so build should work immediately!
```

#### Manual CMake Build
```bash
# Create build directory
mkdir build && cd build

# Configure
cmake -DCMAKE_BUILD_TYPE=Release ..

# Build
cmake --build . --parallel
```

### Running the Applications

#### Qt5 GUI Client

**Quick Start (Recommended):**
```bash
# Complete GUI setup and run (recommended for first time)
./scripts/run.sh gui

# Alternative: Step-by-step approach
./scripts/gui/setup-opengl-container.sh  # Setup OpenGL/Mesa (first time only)
./scripts/run.sh client               # Build and run GUI

# For development workflow
./scripts/run.sh client --headless   # Headless mode for testing

# Clean rebuild in Release mode
./scripts/run.sh client --rebuild --build-type Release
```

**Manual Build and Run:**
```bash
# Build the project
cmake -S . -B build -DCMAKE_BUILD_TYPE=Debug
cmake --build build --target client_app --parallel

# Run with GUI (if X11 forwarding is working)
./build/apps/client/client_app

# Run in headless mode
./build/apps/client/client_app --headless
```

**Host X11 Server Setup:**

Before running GUI applications, you need an X11 server on your host:

```bash
# Auto-detect OS and setup X11 server
./scripts/setup.sh x11

# Platform-specific scripts:
./scripts/platform/setup-xquartz-macos.sh      # macOS
./scripts/platform/setup-vcxsrv-windows.ps1    # Windows (PowerShell as Admin)
```

**Dev Container GUI Setup:**

The devcontainer is pre-configured for GUI applications with X11 forwarding:

- **Linux hosts**: Native X11 support
- **macOS hosts**: Requires XQuartz installation
- **Windows hosts**: Requires VcXsrv/Xming installation
- **Container environment**: Automatically detects DISPLAY and falls back to headless if needed

See [X11 Setup Guide](scripts/README_X11_SETUP.md) for detailed instructions.

**Troubleshooting GUI Issues:**
```bash
# Test X11 connection
xset q

# Test with simple GUI app
xclock

# Check environment variables
echo $DISPLAY
echo $QT_QPA_PLATFORM

# Run setup script to fix common issues
./scripts/setup.sh gui
```

#### HTTP API Server
```bash
# Run the HTTP API server (default: localhost:8080)
./build/bin/server

# Run with custom port
./build/bin/server --port 3000

# Run with custom host and port
./build/bin/server --host 127.0.0.1 --port 3000

# Show help
./build/bin/server --help
```

**API Endpoints:**
- `GET /health` - Health check
- `GET /api/v1/info` - API information
- `POST /api/v1/arcface/infer` - ArcFace face recognition
- `POST /api/v1/yolo/detect` - YOLO object detection

**Example API Usage:**
```bash
# Health check
curl http://localhost:8080/health

# ArcFace inference
curl -X POST http://localhost:8080/api/v1/arcface/infer \
  -H "Content-Type: application/json" \
  -d '{"image_path": "/path/to/face.jpg"}'

# YOLO detection
curl -X POST http://localhost:8080/api/v1/yolo/detect \
  -H "Content-Type: application/json" \
  -d '{"image_path": "/path/to/image.jpg"}'
```

**Test the API:**
```bash
# Run comprehensive API tests
./apps/server/test_api.sh
```

#### Utility Examples
```bash
# Run utility demonstrations
./build/bin/examples/string_utils_demo
./build/bin/examples/datetime_utils_demo
./build/bin/examples/file_utils_demo
./build/bin/examples/json_utils_demo
./build/bin/examples/number_utils_demo
```

### Running Tests

```bash
# Run all tests
cd build && ctest

# Run tests with verbose output
ctest --verbose

# Run specific test suites
ctest -R "models_.*"    # Model tests
ctest -R "shared_.*"    # Utility tests
```

## 🚢 Deployment to Orange Pi

C-AIBOX supports flexible Docker-based deployment to Orange Pi devices with ARM64-compatible containers.

### 🚀 Quick Deployment (Recommended)

**Automated deployment with ARM64-compatible simple services:**

```bash
# Deploy to default Orange Pi (***************)
./scripts/deploy.sh

# Deploy to custom host
./scripts/deploy.sh --host ************* --user myuser

# Monitor deployed services
./scripts/monitor.sh
```

**Access your deployed application:**
- **🌐 Web Interface**: http://***************:3000
- **🔧 Server API**: http://***************:8080
- **📊 API Status**: http://***************:8080/api/status

### 🏗️ Simple Deployment Architecture

The simple deployment uses ARM64-native containers built directly on the Orange Pi:

```
┌─────────────────────────────────────────────────────────────┐
│                    Orange Pi Device                         │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                Docker Network                           ││
│  │  ┌─────────────────────┐  ┌─────────────────────────────┐││
│  │  │  Simple Server      │  │  Simple Web Client          │││
│  │  │  (Node.js/Express)  │  │  (Nginx + HTML/JS)          │││
│  │  │  Port: 8080         │  │  Ports: 3000, 8081          │││
│  │  │  API Endpoints      │  │  Web Interface              │││
│  │  └─────────────────────┘  └─────────────────────────────┘││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 🧪 Testing the Deployment

```bash
# Health check
curl http://***************:8080/health

# API status
curl http://***************:8080/api/status

# List models
curl http://***************:8080/api/models

# Run inference
curl -X POST http://***************:8080/api/inference \
  -H "Content-Type: application/json" \
  -d '{"model":"simple-model-1","input":"test"}'
```

### 📊 Monitoring Services

```bash
# Basic monitoring
./scripts/monitor.sh

# Show container logs
./scripts/monitor.sh --logs

# Test all API endpoints
./scripts/monitor.sh --test
```

### 🔧 Advanced Deployment (C++ Applications)

For the full C++ application stack with Qt5 GUI:

1. **Configure Environment**:
   ```bash
   cp .env.template .env
   # Edit .env with your Orange Pi IP and settings
   ```

2. **Deploy Combined Stack** (Server + Qt5 Client):
   ```bash
   ./scripts/deploy/deploy-docker-compose.sh --ip *************** --profile combined
   ```

3. **Access the Application**:
   - **API Server**: `http://***************:8080`
   - **GUI via VNC**: `***************:5900` (password: `c-aibox123`)
   - **Web Interface**: `http://***************:8081`

### 📚 Documentation

- **[📖 Deployment Guide](DEPLOYMENT.md)** - Complete deployment instructions
- **[🐳 Docker Configuration](docker/README.md)** - Container setup and configuration

### 🎯 Current Deployment Status

✅ **Working Features:**
- ARM64-compatible Docker images
- Automated deployment to Orange Pi
- Simple mock server with API endpoints
- Interactive web client interface
- Service monitoring and health checks

🚧 **Future Enhancements:**
- Integration with C++ AI models
- Qt5 GUI client deployment
- Authentication and authorization
- Production-ready configurations

## 🛠️ Implemented Features

### Qt5 GUI Application (Implemented ✅)

The client application provides a professional desktop interface built with Qt5:

<augment_code_snippet path="apps/client/src/ui/main_window.cpp" mode="EXCERPT">
````cpp
namespace ui {

MainWindow::MainWindow(QWidget* parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_toolbar(nullptr)
    , m_isFullscreen(false) {

    setupWindow();
    setupUI();
    setupConnections();
    applyStylesheet();
}
````
</augment_code_snippet>

**Features:**
- Modern Qt5 architecture with proper MVC separation
- Custom widgets for camera feed, person list, system info
- Professional UI with responsive design
- Cross-platform compatibility (Windows, Linux, macOS)

### HTTP API Server (Implemented ✅)

The server application provides a RESTful HTTP API for AI model inference using cpp-httplib:

<augment_code_snippet path="apps/server/src/main.cpp" mode="EXCERPT">
````cpp
class AIBoxServer {
private:
    httplib::Server server;
    ServerConfig config;

public:
    AIBoxServer(const ServerConfig& cfg = {}) : config(cfg) {
        setupRoutes();
        setupMiddleware();
    }
````
</augment_code_snippet>

**Features:**
- RESTful API with JSON responses
- ArcFace face recognition endpoint
- YOLO object detection endpoint
- CORS support for web applications
- Request logging and error handling
- Command-line configuration (host, port)
- Health monitoring and API information endpoints

## 🔧 Development Guide

### Dev Container Development

This project includes a fully configured VSCode dev container for seamless development:

**Container Configuration:**
- **Base Image**: Ubuntu 24.04 LTS
- **Compiler**: GCC 11+ with C++20 support
- **Build System**: CMake 3.18+, Ninja
- **GUI Framework**: Qt5 (Core, Widgets, GUI, Network, WebEngine, Multimedia)
- **Testing**: GoogleTest (automatically downloaded)
- **Tools**: Git, pkg-config, build-essential, clang-tools
- **OpenGL**: Mesa drivers, software rendering support

**VSCode Extensions (50+ Professional Extensions):**
- **Core C++**: IntelliSense, debugging, code browsing, themes
- **Build Systems**: CMake Tools, Makefile support
- **Advanced Debugging**: LLDB, memory inspector, embedded tools, Valgrind integration
- **Code Quality**: Clang-tidy, clang-format, static analysis, documentation generation
- **Qt Development**: Qt tools, GUI debugging support, .ui/.qrc file support
- **Memory Analysis**: Memory inspector, hex editor, performance analysis
- **Git Integration**: GitLens, Git Graph, history visualization
- **Productivity**: Code runner, better comments, spell checker, themes

See [Debug Extensions Guide](docs/debug-extensions-guide.md) for complete debugging setup.

**Benefits:**
- ✅ **Instant Setup** - No manual dependency installation
- ✅ **Consistent Environment** - Same setup across all team members
- ✅ **Isolated Development** - No conflicts with host system
- ✅ **Integrated Debugging** - Full VSCode debugging support
- ✅ **Port Forwarding** - Access applications running in container

## � Documentation

### Technical Documentation
- **[Project Structure Guidelines](docs/project-structure-guideline.md)** - Architecture and organization
- **[Coding Conventions](docs/coding-convention-naming-guideline.md)** - Code style and naming
- **[Development Setup Guide](docs/setup-development-guideline.md)** - Environment configuration

### Research Documentation
- **[Task Breakdown](docs/tasks-defined/)** - Detailed task definitions and planning
- **[Deep Research](docs/deep-research/)** - Technical analysis for future AI implementation

### API Documentation
The utility libraries are well-documented with comprehensive examples. See the `examples/` directory for working demonstrations of all functionality.

## 🚀 Future Roadmap

### Phase 1: Core AI Integration (Planned)
- **OpenCV Integration** - Computer vision operations
- **RKNN Runtime** - NPU acceleration for RK3588
- **Real Model Implementation** - Replace stubs with actual AI models
- **RTSP Stream Processing** - Video input pipeline

### Phase 2: Advanced Features (Planned)
- **Multi-camera Support** - Simultaneous stream processing
- **Database Integration** - Person recognition database
- **REST API** - Web service interface
- **Docker Deployment** - Containerized deployment

### Phase 3: Production Features (Planned)
- **Performance Optimization** - Hardware acceleration tuning
- **Monitoring & Logging** - Production-ready observability
- **Configuration Management** - Runtime configuration
- **Security Features** - Authentication and authorization

## 🤝 Contributing

### Development Workflow
1. **Fork the repository** and create a feature branch
2. **Follow coding conventions** defined in the documentation
3. **Write comprehensive tests** for new functionality
4. **Update documentation** as needed
5. **Submit a pull request** with detailed description

### Code Review Guidelines
- All code must pass automated tests
- Follow the established architecture patterns
- Maintain backward compatibility
- Document public APIs thoroughly

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Qt Project** for the excellent GUI framework
- **Google Test** for the testing framework
- **CMake** for the build system
- **Contributors** who have helped improve this project

---

**Note**: This project currently provides a solid foundation for AI application development. The AI-specific features (RKNN, OpenCV, RTSP) are planned for future implementation and the architecture is designed to accommodate them seamlessly.


