#!/bin/bash

# C-AIBOX Deployment Script
# This script deploys the C-AIBOX application to a remote Orange Pi device

set -e

# Configuration
REMOTE_HOST="${REMOTE_HOST:-***************}"
REMOTE_USER="${REMOTE_USER:-orangepi}"
DEPLOY_DIR="${DEPLOY_DIR:-/home/<USER>/c-aibox-deploy}"
IMAGE_TAG="${IMAGE_TAG:-latest}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if remote host is reachable
check_connectivity() {
    log_info "Checking connectivity to $REMOTE_USER@$REMOTE_HOST..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes $REMOTE_USER@$REMOTE_HOST exit 2>/dev/null; then
        log_success "Successfully connected to remote host"
    else
        log_error "Cannot connect to $REMOTE_USER@$REMOTE_HOST"
        log_error "Please check your SSH configuration and network connectivity"
        exit 1
    fi
}

# Create deployment directory on remote host
setup_remote_directory() {
    log_info "Setting up deployment directory on remote host..."
    ssh $REMOTE_USER@$REMOTE_HOST "mkdir -p $DEPLOY_DIR/{data,logs,models}"
    log_success "Deployment directory created"
}

# Build simple ARM64-compatible images
build_simple_images() {
    log_info "Building simple ARM64-compatible images..."
    
    # Transfer simple server source
    log_info "Transferring simple server source..."
    scp -r simple-server $REMOTE_USER@$REMOTE_HOST:$DEPLOY_DIR/
    
    # Transfer simple web client source
    log_info "Transferring simple web client source..."
    scp -r simple-web-client $REMOTE_USER@$REMOTE_HOST:$DEPLOY_DIR/
    
    # Build simple server on remote host
    log_info "Building simple server on remote host..."
    ssh $REMOTE_USER@$REMOTE_HOST "cd $DEPLOY_DIR/simple-server && docker build -t c-aibox-simple-server:$IMAGE_TAG ."
    
    # Build simple web client on remote host
    log_info "Building simple web client on remote host..."
    ssh $REMOTE_USER@$REMOTE_HOST "cd $DEPLOY_DIR/simple-web-client && docker build -t c-aibox-simple-web-client:$IMAGE_TAG ."
    
    log_success "Simple images built successfully on remote host"
}

# Transfer configuration files
transfer_configs() {
    log_info "Transferring configuration files..."
    
    # Transfer docker-compose files
    scp docker-compose.yml docker-compose.deploy.yml $REMOTE_USER@$REMOTE_HOST:$DEPLOY_DIR/ 2>/dev/null || log_warning "Some compose files not found"
    
    # Transfer docker directory
    scp -r docker $REMOTE_USER@$REMOTE_HOST:$DEPLOY_DIR/ 2>/dev/null || log_warning "Docker directory not found"
    
    # Transfer environment file if it exists
    if [ -f .env ]; then
        scp .env $REMOTE_USER@$REMOTE_HOST:$DEPLOY_DIR/
    else
        log_warning ".env file not found, creating default..."
        ssh $REMOTE_USER@$REMOTE_HOST "cat > $DEPLOY_DIR/.env << 'EOF'
# C-AIBOX Environment Configuration
IMAGE_TAG=$IMAGE_TAG
SERVER_HOST_PORT=8080
CLIENT_VNC_PORT=5900
CLIENT_WEB_PORT=3000
VNC_PASSWORD=c-aibox123
EOF"
    fi
    
    log_success "Configuration files transferred"
}

# Deploy simple services
deploy_simple_services() {
    log_info "Deploying simple services on remote host..."
    
    # Stop any existing services
    ssh $REMOTE_USER@$REMOTE_HOST "cd $DEPLOY_DIR && docker rm -f c-aibox-simple-server c-aibox-simple-web-client 2>/dev/null || true"
    
    # Create network
    ssh $REMOTE_USER@$REMOTE_HOST "docker network create c-aibox-network 2>/dev/null || true"
    
    # Start simple server
    log_info "Starting simple server..."
    ssh $REMOTE_USER@$REMOTE_HOST "cd $DEPLOY_DIR && docker run -d --name c-aibox-simple-server --network c-aibox-network -p 8080:8080 -v ./data:/app/data -v ./logs:/app/logs -v ./models:/app/models c-aibox-simple-server:$IMAGE_TAG"
    
    # Wait for server to start
    sleep 5
    
    # Start simple web client
    log_info "Starting simple web client..."
    ssh $REMOTE_USER@$REMOTE_HOST "cd $DEPLOY_DIR && docker run -d --name c-aibox-simple-web-client --network c-aibox-network -p 3000:3000 -p 8081:80 -v ./data:/app/data -v ./logs:/app/logs c-aibox-simple-web-client:$IMAGE_TAG"
    
    log_success "Simple services deployed successfully"
}

# Check deployment status
check_deployment() {
    log_info "Checking deployment status..."
    
    # Check running containers
    log_info "Running containers:"
    ssh $REMOTE_USER@$REMOTE_HOST "docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'"
    
    # Check service health
    log_info "Checking service health..."
    sleep 5
    
    if ssh $REMOTE_USER@$REMOTE_HOST "curl -s http://localhost:8080/health" >/dev/null 2>&1; then
        log_success "Server health check passed"
    else
        log_warning "Server health check failed"
    fi
    
    log_success "Deployment completed!"
    log_info "Access the application at:"
    log_info "  Server API: http://$REMOTE_HOST:8080"
    log_info "  Web Client: http://$REMOTE_HOST:3000"
    log_info "  Web Client (Alt): http://$REMOTE_HOST:8081"
    log_info "  VNC Client: vnc://$REMOTE_HOST:5900"
}

# Main deployment function for simple services
deploy_simple() {
    log_info "Starting C-AIBOX simple deployment to $REMOTE_USER@$REMOTE_HOST"
    
    check_connectivity
    setup_remote_directory
    build_simple_images
    transfer_configs
    deploy_simple_services
    check_deployment
    
    log_success "Simple deployment completed successfully!"
}

# Parse command line arguments
DEPLOY_MODE="simple"  # Default to simple deployment

while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            REMOTE_HOST="$2"
            shift 2
            ;;
        --user)
            REMOTE_USER="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --host HOST      Remote host (default: ***************)"
            echo "  --user USER      Remote user (default: orangepi)"
            echo "  --tag TAG        Image tag (default: latest)"
            echo "  --help           Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run simple deployment
deploy_simple
