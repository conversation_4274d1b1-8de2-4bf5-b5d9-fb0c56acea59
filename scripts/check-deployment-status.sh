#!/bin/bash

# Quick Deployment Status Check for C-AIBOX
# Shows current status of deployment readiness

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo -e "${BOLD}${CYAN}=== C-AIBOX Deployment Status Check ===${NC}"
echo ""

cd "$PROJECT_ROOT"

# Check binaries
echo -e "${BLUE}📦 Built Binaries:${NC}"
if [[ -f "build-container/bin/server" ]]; then
    echo -e "  ${GREEN}✓${NC} Server binary exists"
else
    echo -e "  ${RED}✗${NC} Server binary missing"
fi

if [[ -f "build-container/bin/client_app" ]]; then
    echo -e "  ${GREEN}✓${NC} Client binary exists"
else
    echo -e "  ${RED}✗${NC} Client binary missing"
fi

# Check configuration
echo ""
echo -e "${BLUE}⚙️  Configuration:${NC}"
if [[ -f ".env" ]]; then
    echo -e "  ${GREEN}✓${NC} .env file exists"
    
    # Check key settings
    if grep -q "^ORANGE_PI_IP=" .env; then
        ORANGE_PI_IP=$(grep "^ORANGE_PI_IP=" .env | cut -d'=' -f2)
        echo -e "  ${GREEN}✓${NC} Orange Pi IP: $ORANGE_PI_IP"
    else
        echo -e "  ${YELLOW}⚠${NC} Orange Pi IP not set"
    fi
    
    if grep -q "^CLIENT_TYPE=" .env; then
        CLIENT_TYPE=$(grep "^CLIENT_TYPE=" .env | cut -d'=' -f2)
        echo -e "  ${GREEN}✓${NC} Client type: $CLIENT_TYPE"
    else
        echo -e "  ${YELLOW}⚠${NC} Client type not set"
    fi
else
    echo -e "  ${RED}✗${NC} .env file missing (copy from .env.template)"
fi

# Check Docker files
echo ""
echo -e "${BLUE}🐳 Docker Files:${NC}"
for dockerfile in "docker/Dockerfile.combined" "docker/Dockerfile.client-gui" "docker/Dockerfile.web-client" "docker/Dockerfile.arm64"; do
    if [[ -f "$dockerfile" ]]; then
        echo -e "  ${GREEN}✓${NC} $(basename $dockerfile)"
    else
        echo -e "  ${RED}✗${NC} $(basename $dockerfile) missing"
    fi
done

if [[ -f "docker-compose.yml" ]]; then
    echo -e "  ${GREEN}✓${NC} docker-compose.yml"
else
    echo -e "  ${RED}✗${NC} docker-compose.yml missing"
fi

# Check deployment scripts
echo ""
echo -e "${BLUE}🚀 Deployment Scripts:${NC}"
for script in "scripts/deploy/deploy-docker-compose.sh" "scripts/deploy/deploy-client-gui.sh" "scripts/deploy/quick-deploy-demo.sh"; do
    if [[ -f "$script" && -x "$script" ]]; then
        echo -e "  ${GREEN}✓${NC} $(basename $script)"
    elif [[ -f "$script" ]]; then
        echo -e "  ${YELLOW}⚠${NC} $(basename $script) (not executable)"
    else
        echo -e "  ${RED}✗${NC} $(basename $script) missing"
    fi
done

# Check tools
echo ""
echo -e "${BLUE}🛠️  Required Tools:${NC}"
if command -v docker >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓${NC} Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1)"
else
    echo -e "  ${RED}✗${NC} Docker not installed"
fi

if command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓${NC} Docker Compose $(docker compose version | cut -d' ' -f4)"
else
    echo -e "  ${RED}✗${NC} Docker Compose not available"
fi

if command -v ssh >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓${NC} SSH client available"
else
    echo -e "  ${RED}✗${NC} SSH client not available"
fi

# Quick deployment commands
echo ""
echo -e "${BOLD}${CYAN}🚀 Quick Deployment Commands:${NC}"
echo ""

if [[ -f ".env" ]] && grep -q "^ORANGE_PI_IP=" .env; then
    ORANGE_PI_IP=$(grep "^ORANGE_PI_IP=" .env | cut -d'=' -f2)
    echo -e "${YELLOW}Using Orange Pi IP from .env: $ORANGE_PI_IP${NC}"
    echo ""
    echo "1. Combined deployment (recommended):"
    echo "   ./scripts/deploy/deploy-docker-compose.sh --ip $ORANGE_PI_IP --profile combined"
    echo ""
    echo "2. Interactive demo:"
    echo "   ./scripts/deploy/quick-deploy-demo.sh --ip $ORANGE_PI_IP"
    echo ""
    echo "3. Client GUI only:"
    echo "   ./scripts/deploy/deploy-client-gui.sh --ip $ORANGE_PI_IP --type qt5"
    echo ""
    echo "4. Web client only:"
    echo "   ./scripts/deploy/deploy-client-gui.sh --ip $ORANGE_PI_IP --type web"
else
    echo -e "${YELLOW}Configure .env file first:${NC}"
    echo "   cp .env.template .env"
    echo "   # Edit .env with your Orange Pi IP"
    echo ""
    echo "Then run deployment:"
    echo "   ./scripts/deploy/quick-deploy-demo.sh --ip YOUR_ORANGE_PI_IP"
fi

echo ""
echo -e "${BOLD}${CYAN}📚 Documentation:${NC}"
echo "   README.md - Main documentation"
echo "   docs/CLIENT_GUI_DEPLOYMENT_GUIDE.md - Detailed deployment guide"
echo "   .env.template - Configuration template with examples"

echo ""
echo -e "${BOLD}${GREEN}Ready to deploy? Run the commands above!${NC}"
