#!/bin/bash

# Client GUI Deployment Script for C-AIBOX
# Deploys client GUI (Qt5 or Web-based) to Orange Pi with Docker support

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
ORANGE_PI_IP=""
ORANGE_PI_USER="orangepi"
ORANGE_PI_PORT="22"
SSH_KEY=""
CLIENT_TYPE="qt5"  # qt5, web, both
BUILD_IMAGES=true
TRANSFER_METHOD="save"
START_SERVICES=true
REMOVE_OLD=true
VNC_ENABLED=true
WEB_CLIENT_ENABLED=false

# Load environment configuration
if [[ -f "$PROJECT_ROOT/.env" ]]; then
    source "$PROJECT_ROOT/.env"
    SSH_KEY="$ORANGE_PI_SSH_KEY"
    CLIENT_TYPE="${CLIENT_TYPE:-qt5}"
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --user)
            ORANGE_PI_USER="$2"
            shift 2
            ;;
        --type)
            CLIENT_TYPE="$2"
            shift 2
            ;;
        --no-build)
            BUILD_IMAGES=false
            shift
            ;;
        --no-start)
            START_SERVICES=false
            shift
            ;;
        --no-vnc)
            VNC_ENABLED=false
            shift
            ;;
        --enable-web)
            WEB_CLIENT_ENABLED=true
            shift
            ;;
        --help)
            cat << EOF
Client GUI Deployment Script for C-AIBOX

USAGE:
    $0 --ip IP_ADDRESS [OPTIONS]

REQUIRED:
    --ip IP             Orange Pi IP address

OPTIONS:
    --user USER         SSH username (default: orangepi)
    --type TYPE         Client type: qt5, web, both (default: qt5)
    --no-build          Skip building images locally
    --no-start          Don't start services after deployment
    --no-vnc            Disable VNC server for Qt5 client
    --enable-web        Enable web client alongside Qt5
    --help              Show this help

CLIENT TYPES:
    qt5                 Deploy Qt5 desktop client with VNC access
    web                 Deploy web-based client with HTTP access
    both                Deploy both Qt5 and web clients

EXAMPLES:
    $0 --ip ***************                    # Deploy Qt5 client
    $0 --ip *************** --type web         # Deploy web client
    $0 --ip *************** --type both        # Deploy both clients
EOF
            exit 0
            ;;
        *)
            if [[ -z "$ORANGE_PI_IP" && "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                ORANGE_PI_IP="$1"
            else
                log_error "Unknown option: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ORANGE_PI_IP" ]]; then
    log_error "Orange Pi IP address is required"
    exit 1
fi

# Validate client type
if [[ "$CLIENT_TYPE" != "qt5" && "$CLIENT_TYPE" != "web" && "$CLIENT_TYPE" != "both" ]]; then
    log_error "Invalid client type: $CLIENT_TYPE. Must be qt5, web, or both"
    exit 1
fi

# SSH command builder
build_ssh_cmd() {
    local ssh_cmd="ssh"
    if [[ -n "$SSH_KEY" ]]; then
        ssh_cmd="$ssh_cmd -i $SSH_KEY"
    fi
    ssh_cmd="$ssh_cmd -p $ORANGE_PI_PORT $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "$ssh_cmd"
}

# SCP command builder
build_scp_cmd() {
    local scp_cmd="scp"
    if [[ -n "$SSH_KEY" ]]; then
        scp_cmd="$scp_cmd -i $SSH_KEY"
    fi
    scp_cmd="$scp_cmd -P $ORANGE_PI_PORT"
    echo "$scp_cmd"
}

# Test SSH connection
test_ssh_connection() {
    log_step "Testing SSH Connection"
    local ssh_cmd=$(build_ssh_cmd)
    
    if $ssh_cmd "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "SSH connection failed to $ORANGE_PI_IP"
        exit 1
    fi
}

# Check Docker on Orange Pi
check_docker_on_device() {
    log_step "Checking Docker on Orange Pi"
    local ssh_cmd=$(build_ssh_cmd)
    
    if $ssh_cmd "docker --version" >/dev/null 2>&1; then
        log_success "Docker is available on Orange Pi"
    else
        log_error "Docker is not installed on Orange Pi"
        log_info "Install Docker first: curl -fsSL https://get.docker.com | sh"
        exit 1
    fi
    
    # Check Docker Compose
    if $ssh_cmd "docker compose version" >/dev/null 2>&1; then
        log_success "Docker Compose is available"
    else
        log_warning "Docker Compose not found, trying docker-compose..."
        if $ssh_cmd "docker-compose --version" >/dev/null 2>&1; then
            log_success "docker-compose is available"
        else
            log_error "Docker Compose is not available"
            exit 1
        fi
    fi
}

# Build images locally
build_images() {
    if [[ "$BUILD_IMAGES" != "true" ]]; then
        return
    fi
    
    log_step "Building Client Images"
    cd "$PROJECT_ROOT"
    
    case "$CLIENT_TYPE" in
        qt5)
            log_info "Building Qt5 client image..."
            docker build -f docker/Dockerfile.client-gui -t ${CLIENT_IMAGE_NAME:-c-aibox-client}:${IMAGE_TAG:-latest} .
            ;;
        web)
            log_info "Building web client image..."
            docker build -f docker/Dockerfile.web-client -t ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest} .
            ;;
        both)
            log_info "Building Qt5 client image..."
            docker build -f docker/Dockerfile.client-gui -t ${CLIENT_IMAGE_NAME:-c-aibox-client}:${IMAGE_TAG:-latest} .
            log_info "Building web client image..."
            docker build -f docker/Dockerfile.web-client -t ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest} .
            ;;
    esac
    
    log_success "Client images built successfully"
}

# Transfer files to Orange Pi
transfer_files() {
    log_step "Transferring Files to Orange Pi"
    local ssh_cmd=$(build_ssh_cmd)
    local scp_cmd=$(build_scp_cmd)
    
    # Create deployment directory
    $ssh_cmd "mkdir -p /home/<USER>/c-aibox-client-deploy"
    
    # Transfer docker-compose.yml
    log_info "Transferring docker-compose.yml..."
    $scp_cmd "$PROJECT_ROOT/docker-compose.yml" "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-client-deploy/"
    
    # Transfer .env file
    log_info "Transferring .env configuration..."
    $scp_cmd "$PROJECT_ROOT/.env" "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-client-deploy/"
    
    # Create data directories
    $ssh_cmd "mkdir -p /home/<USER>/c-aibox-client-deploy/{data,logs}"
    
    log_success "Files transferred successfully"
}

# Transfer Docker images
transfer_images() {
    log_step "Transferring Docker Images"
    local ssh_cmd=$(build_ssh_cmd)
    local scp_cmd=$(build_scp_cmd)
    
    cd "$PROJECT_ROOT"
    
    case "$CLIENT_TYPE" in
        qt5)
            log_info "Saving and transferring Qt5 client image..."
            docker save ${CLIENT_IMAGE_NAME:-c-aibox-client}:${IMAGE_TAG:-latest} | gzip > client-image.tar.gz
            $scp_cmd client-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-client-deploy/"
            $ssh_cmd "cd /home/<USER>/c-aibox-client-deploy && gunzip -c client-image.tar.gz | docker load"
            rm client-image.tar.gz
            ;;
        web)
            log_info "Saving and transferring web client image..."
            docker save ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest} | gzip > web-client-image.tar.gz
            $scp_cmd web-client-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-client-deploy/"
            $ssh_cmd "cd /home/<USER>/c-aibox-client-deploy && gunzip -c web-client-image.tar.gz | docker load"
            rm web-client-image.tar.gz
            ;;
        both)
            log_info "Saving and transferring Qt5 client image..."
            docker save ${CLIENT_IMAGE_NAME:-c-aibox-client}:${IMAGE_TAG:-latest} | gzip > client-image.tar.gz
            $scp_cmd client-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-client-deploy/"
            
            log_info "Saving and transferring web client image..."
            docker save ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest} | gzip > web-client-image.tar.gz
            $scp_cmd web-client-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-client-deploy/"
            
            $ssh_cmd "cd /home/<USER>/c-aibox-client-deploy && gunzip -c client-image.tar.gz | docker load && gunzip -c web-client-image.tar.gz | docker load"
            rm client-image.tar.gz web-client-image.tar.gz
            ;;
    esac
    
    log_success "Images transferred successfully"
}

# Deploy with Docker Compose
deploy_services() {
    log_step "Deploying Client Services with Docker Compose"
    local ssh_cmd=$(build_ssh_cmd)
    
    # Determine compose profile
    local profile=""
    case "$CLIENT_TYPE" in
        qt5)
            profile="separate"
            ;;
        web)
            profile="web-client"
            ;;
        both)
            profile="separate,web-client"
            ;;
    esac
    
    # Stop existing services
    if [[ "$REMOVE_OLD" == "true" ]]; then
        log_info "Stopping existing client services..."
        $ssh_cmd "cd /home/<USER>/c-aibox-client-deploy && docker compose --profile $profile down" || true
    fi
    
    if [[ "$START_SERVICES" == "true" ]]; then
        log_info "Starting client services with profile: $profile"
        $ssh_cmd "cd /home/<USER>/c-aibox-client-deploy && docker compose --profile $profile up -d"
        
        # Wait for services to start
        sleep 10
        
        # Check service status
        $ssh_cmd "cd /home/<USER>/c-aibox-client-deploy && docker compose --profile $profile ps"
    fi
    
    log_success "Client services deployed successfully"
}

# Show deployment summary
show_summary() {
    log_step "Client Deployment Summary"
    local ssh_cmd=$(build_ssh_cmd)
    
    echo "Target: $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "Client Type: $CLIENT_TYPE"
    echo ""
    
    # Show running containers
    log_info "Running containers:"
    local profile=""
    case "$CLIENT_TYPE" in
        qt5) profile="separate" ;;
        web) profile="web-client" ;;
        both) profile="separate,web-client" ;;
    esac
    $ssh_cmd "cd /home/<USER>/c-aibox-client-deploy && docker compose --profile $profile ps" || true
    
    echo ""
    log_success "Client GUI deployment completed successfully!"
    log_info "Access options:"
    
    if [[ "$CLIENT_TYPE" == "qt5" || "$CLIENT_TYPE" == "both" ]]; then
        if [[ "$VNC_ENABLED" == "true" ]]; then
            log_info "  1. Qt5 GUI via VNC: $ORANGE_PI_IP:${CLIENT_VNC_PORT:-5900}"
            log_info "     VNC Password: ${VNC_PASSWORD:-c-aibox123}"
        fi
        log_info "  2. Web placeholder: http://$ORANGE_PI_IP:${CLIENT_WEB_NGINX_PORT:-8081}"
    fi
    
    if [[ "$CLIENT_TYPE" == "web" || "$CLIENT_TYPE" == "both" ]]; then
        log_info "  3. Web Client: http://$ORANGE_PI_IP:${CLIENT_WEB_PORT:-3000}"
        log_info "  4. Web Client (Nginx): http://$ORANGE_PI_IP:${CLIENT_WEB_NGINX_PORT:-8081}"
    fi
    
    log_info "  5. View logs: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'cd c-aibox-client-deploy && docker compose logs'"
    log_info "  6. Stop services: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'cd c-aibox-client-deploy && docker compose down'"
}

# Main execution
main() {
    log_step "C-AIBOX Client GUI Deployment to Orange Pi"
    
    test_ssh_connection
    check_docker_on_device
    build_images
    transfer_files
    transfer_images
    deploy_services
    show_summary
}

# Run main function
main "$@"
