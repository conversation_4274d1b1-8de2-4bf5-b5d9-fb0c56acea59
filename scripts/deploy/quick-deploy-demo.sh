#!/bin/bash

# Quick Deployment Demo Script for C-AIBOX Client GUI
# Demonstrates various deployment options with environment configuration

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
ORANGE_PI_IP=""
DEMO_MODE="interactive"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --auto)
            DEMO_MODE="auto"
            shift
            ;;
        --help)
            cat << EOF
Quick Deployment Demo Script for C-AIBOX Client GUI

USAGE:
    $0 --ip IP_ADDRESS [OPTIONS]

REQUIRED:
    --ip IP             Orange Pi IP address

OPTIONS:
    --auto              Run in automatic mode (no user interaction)
    --help              Show this help

EXAMPLES:
    $0 --ip ***************         # Interactive demo
    $0 --ip *************** --auto  # Automatic demo
EOF
            exit 0
            ;;
        *)
            if [[ -z "$ORANGE_PI_IP" && "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                ORANGE_PI_IP="$1"
            else
                log_error "Unknown option: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ORANGE_PI_IP" ]]; then
    log_error "Orange Pi IP address is required"
    exit 1
fi

# Interactive menu
show_menu() {
    echo ""
    log_step "C-AIBOX Client GUI Deployment Demo"
    echo "Target: $ORANGE_PI_IP"
    echo ""
    echo "Available deployment options:"
    echo "1. Combined Stack (Server + Qt5 Client)"
    echo "2. Separate Containers (Server + Qt5 Client)"
    echo "3. Web Client Only"
    echo "4. Both Qt5 and Web Clients"
    echo "5. Client GUI Only (Qt5)"
    echo "6. Client GUI Only (Web)"
    echo "7. Client GUI Only (Both)"
    echo "8. Show Current Status"
    echo "9. Stop All Services"
    echo "0. Exit"
    echo ""
}

# Get user choice
get_choice() {
    if [[ "$DEMO_MODE" == "auto" ]]; then
        echo "1"  # Default to combined stack in auto mode
    else
        read -p "Enter your choice (0-9): " choice
        echo "$choice"
    fi
}

# Deploy combined stack
deploy_combined() {
    log_step "Deploying Combined Stack (Server + Qt5 Client)"
    "$SCRIPT_DIR/deploy-docker-compose.sh" --ip "$ORANGE_PI_IP" --profile combined
    
    echo ""
    log_success "Combined stack deployed successfully!"
    log_info "Access options:"
    log_info "  - API Server: http://$ORANGE_PI_IP:8080"
    log_info "  - Qt5 GUI via VNC: $ORANGE_PI_IP:5900 (password: c-aibox123)"
    log_info "  - Web Placeholder: http://$ORANGE_PI_IP:8081"
}

# Deploy separate containers
deploy_separate() {
    log_step "Deploying Separate Containers (Server + Qt5 Client)"
    "$SCRIPT_DIR/deploy-docker-compose.sh" --ip "$ORANGE_PI_IP" --profile separate --client-type qt5
    
    echo ""
    log_success "Separate containers deployed successfully!"
    log_info "Access options:"
    log_info "  - API Server: http://$ORANGE_PI_IP:8080"
    log_info "  - Qt5 GUI via VNC: $ORANGE_PI_IP:5900 (password: c-aibox123)"
    log_info "  - Web Placeholder: http://$ORANGE_PI_IP:8081"
}

# Deploy web client only
deploy_web_only() {
    log_step "Deploying Web Client Only"
    "$SCRIPT_DIR/deploy-docker-compose.sh" --ip "$ORANGE_PI_IP" --profile web-client
    
    echo ""
    log_success "Web client deployed successfully!"
    log_info "Access options:"
    log_info "  - Web Client: http://$ORANGE_PI_IP:3000"
    log_info "  - Web Client (Nginx): http://$ORANGE_PI_IP:8081"
}

# Deploy both clients
deploy_both_clients() {
    log_step "Deploying Both Qt5 and Web Clients"
    "$SCRIPT_DIR/deploy-docker-compose.sh" --ip "$ORANGE_PI_IP" --profile separate --client-type both
    
    echo ""
    log_success "Both clients deployed successfully!"
    log_info "Access options:"
    log_info "  - API Server: http://$ORANGE_PI_IP:8080"
    log_info "  - Qt5 GUI via VNC: $ORANGE_PI_IP:5900 (password: c-aibox123)"
    log_info "  - Web Client: http://$ORANGE_PI_IP:3000"
    log_info "  - Web Placeholder: http://$ORANGE_PI_IP:8081"
}

# Deploy client GUI only (Qt5)
deploy_client_qt5_only() {
    log_step "Deploying Client GUI Only (Qt5)"
    "$SCRIPT_DIR/deploy-client-gui.sh" --ip "$ORANGE_PI_IP" --type qt5
    
    echo ""
    log_success "Qt5 client GUI deployed successfully!"
    log_info "Access options:"
    log_info "  - Qt5 GUI via VNC: $ORANGE_PI_IP:5900 (password: c-aibox123)"
    log_info "  - Web Placeholder: http://$ORANGE_PI_IP:8081"
}

# Deploy client GUI only (Web)
deploy_client_web_only() {
    log_step "Deploying Client GUI Only (Web)"
    "$SCRIPT_DIR/deploy-client-gui.sh" --ip "$ORANGE_PI_IP" --type web
    
    echo ""
    log_success "Web client GUI deployed successfully!"
    log_info "Access options:"
    log_info "  - Web Client: http://$ORANGE_PI_IP:3000"
    log_info "  - Web Client (Nginx): http://$ORANGE_PI_IP:8081"
}

# Deploy client GUI only (Both)
deploy_client_both_only() {
    log_step "Deploying Client GUI Only (Both Qt5 and Web)"
    "$SCRIPT_DIR/deploy-client-gui.sh" --ip "$ORANGE_PI_IP" --type both
    
    echo ""
    log_success "Both client GUIs deployed successfully!"
    log_info "Access options:"
    log_info "  - Qt5 GUI via VNC: $ORANGE_PI_IP:5900 (password: c-aibox123)"
    log_info "  - Web Client: http://$ORANGE_PI_IP:3000"
    log_info "  - Web Placeholder: http://$ORANGE_PI_IP:8081"
}

# Show current status
show_status() {
    log_step "Current Deployment Status"
    
    # Test SSH connection
    if ssh -o ConnectTimeout=5 orangepi@$ORANGE_PI_IP "echo 'SSH OK'" >/dev/null 2>&1; then
        log_success "SSH connection: OK"
        
        # Check Docker containers
        log_info "Checking Docker containers..."
        ssh orangepi@$ORANGE_PI_IP "docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'" 2>/dev/null || {
            log_warning "No Docker containers found or Docker not accessible"
        }
        
        # Check services
        echo ""
        log_info "Testing service endpoints..."
        
        # Test API server
        if curl -s -o /dev/null -w "%{http_code}" "http://$ORANGE_PI_IP:8080" | grep -q "200\|404"; then
            log_success "API Server: http://$ORANGE_PI_IP:8080 - Accessible"
        else
            log_warning "API Server: http://$ORANGE_PI_IP:8080 - Not accessible"
        fi
        
        # Test web client
        if curl -s -o /dev/null -w "%{http_code}" "http://$ORANGE_PI_IP:3000" | grep -q "200\|404"; then
            log_success "Web Client: http://$ORANGE_PI_IP:3000 - Accessible"
        else
            log_warning "Web Client: http://$ORANGE_PI_IP:3000 - Not accessible"
        fi
        
        # Test web placeholder
        if curl -s -o /dev/null -w "%{http_code}" "http://$ORANGE_PI_IP:8081" | grep -q "200\|404"; then
            log_success "Web Placeholder: http://$ORANGE_PI_IP:8081 - Accessible"
        else
            log_warning "Web Placeholder: http://$ORANGE_PI_IP:8081 - Not accessible"
        fi
        
        # VNC is harder to test, just mention it
        log_info "VNC Server: $ORANGE_PI_IP:5900 - Use VNC client to test"
        
    else
        log_error "SSH connection failed to $ORANGE_PI_IP"
    fi
}

# Stop all services
stop_services() {
    log_step "Stopping All Services"
    
    ssh orangepi@$ORANGE_PI_IP "cd c-aibox-deploy && docker compose down" 2>/dev/null || {
        log_warning "No services found in c-aibox-deploy directory"
    }
    
    ssh orangepi@$ORANGE_PI_IP "cd c-aibox-client-deploy && docker compose down" 2>/dev/null || {
        log_warning "No services found in c-aibox-client-deploy directory"
    }
    
    log_success "All services stopped"
}

# Main interactive loop
main() {
    log_step "C-AIBOX Client GUI Deployment Demo"
    log_info "Target Orange Pi: $ORANGE_PI_IP"
    
    if [[ "$DEMO_MODE" == "auto" ]]; then
        log_info "Running in automatic mode - deploying combined stack"
        deploy_combined
        return
    fi
    
    while true; do
        show_menu
        choice=$(get_choice)
        
        case $choice in
            1)
                deploy_combined
                ;;
            2)
                deploy_separate
                ;;
            3)
                deploy_web_only
                ;;
            4)
                deploy_both_clients
                ;;
            5)
                deploy_client_qt5_only
                ;;
            6)
                deploy_client_web_only
                ;;
            7)
                deploy_client_both_only
                ;;
            8)
                show_status
                ;;
            9)
                stop_services
                ;;
            0)
                log_info "Exiting demo"
                exit 0
                ;;
            *)
                log_error "Invalid choice: $choice"
                ;;
        esac
        
        if [[ "$DEMO_MODE" != "auto" ]]; then
            echo ""
            read -p "Press Enter to continue..."
        fi
    done
}

# Run main function
main "$@"
