#!/bin/bash

# Docker Compose Deployment Script for C-AIBOX
# Deploys using docker-compose with environment-based configuration

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
ORANGE_PI_IP=""
ORANGE_PI_USER="orangepi"
ORANGE_PI_PORT="22"
SSH_KEY=""
COMPOSE_PROFILE="combined"
CLIENT_TYPE="qt5"
BUILD_IMAGES=true
TRANSFER_METHOD="save"
START_SERVICES=true
REMOVE_OLD=true

# Load environment configuration
if [[ -f "$PROJECT_ROOT/.env" ]]; then
    source "$PROJECT_ROOT/.env"
    SSH_KEY="$ORANGE_PI_SSH_KEY"
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --user)
            ORANGE_PI_USER="$2"
            shift 2
            ;;
        --profile)
            COMPOSE_PROFILE="$2"
            shift 2
            ;;
        --client-type)
            CLIENT_TYPE="$2"
            shift 2
            ;;
        --no-build)
            BUILD_IMAGES=false
            shift
            ;;
        --no-start)
            START_SERVICES=false
            shift
            ;;
        --help)
            cat << EOF
Docker Compose Deployment Script for C-AIBOX

USAGE:
    $0 --ip IP_ADDRESS [OPTIONS]

REQUIRED:
    --ip IP             Orange Pi IP address

OPTIONS:
    --user USER         SSH username (default: orangepi)
    --profile PROFILE   Docker Compose profile: combined, separate, web-client (default: combined)
    --client-type TYPE  Client type for separate mode: qt5, web, both (default: qt5)
    --no-build          Skip building images locally
    --no-start          Don't start services after deployment
    --help              Show this help

EXAMPLES:
    $0 --ip ***************                                    # Deploy combined stack
    $0 --ip *************** --profile separate                 # Deploy separate Qt5 containers
    $0 --ip *************** --profile separate --client-type web # Deploy separate with web client
    $0 --ip *************** --profile web-client               # Deploy web client only
EOF
            exit 0
            ;;
        *)
            if [[ -z "$ORANGE_PI_IP" && "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                ORANGE_PI_IP="$1"
            else
                log_error "Unknown option: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ORANGE_PI_IP" ]]; then
    log_error "Orange Pi IP address is required"
    exit 1
fi

# SSH command builder
build_ssh_cmd() {
    local ssh_cmd="ssh"
    if [[ -n "$SSH_KEY" ]]; then
        ssh_cmd="$ssh_cmd -i $SSH_KEY"
    fi
    ssh_cmd="$ssh_cmd -p $ORANGE_PI_PORT $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "$ssh_cmd"
}

# SCP command builder
build_scp_cmd() {
    local scp_cmd="scp"
    if [[ -n "$SSH_KEY" ]]; then
        scp_cmd="$scp_cmd -i $SSH_KEY"
    fi
    scp_cmd="$scp_cmd -P $ORANGE_PI_PORT"
    echo "$scp_cmd"
}

# Test SSH connection
test_ssh_connection() {
    log_step "Testing SSH Connection"
    local ssh_cmd=$(build_ssh_cmd)
    
    if $ssh_cmd "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "SSH connection failed to $ORANGE_PI_IP"
        exit 1
    fi
}

# Check Docker on Orange Pi
check_docker_on_device() {
    log_step "Checking Docker on Orange Pi"
    local ssh_cmd=$(build_ssh_cmd)
    
    if $ssh_cmd "docker --version" >/dev/null 2>&1; then
        log_success "Docker is available on Orange Pi"
    else
        log_error "Docker is not installed on Orange Pi"
        log_info "Install Docker first: curl -fsSL https://get.docker.com | sh"
        exit 1
    fi
    
    # Check Docker Compose
    if $ssh_cmd "docker compose version" >/dev/null 2>&1; then
        log_success "Docker Compose is available"
    else
        log_warning "Docker Compose not found, trying docker-compose..."
        if $ssh_cmd "docker-compose --version" >/dev/null 2>&1; then
            log_success "docker-compose is available"
        else
            log_error "Docker Compose is not available"
            exit 1
        fi
    fi
}

# Build images locally
build_images() {
    if [[ "$BUILD_IMAGES" != "true" ]]; then
        return
    fi
    
    log_step "Building Docker Images"
    cd "$PROJECT_ROOT"
    
    case "$COMPOSE_PROFILE" in
        combined)
            log_info "Building combined image..."
            docker build -f docker/Dockerfile.combined -t ${COMBINED_IMAGE_NAME:-c-aibox-combined}:${IMAGE_TAG:-latest} .
            ;;
        separate)
            log_info "Building server image..."
            docker build -f docker/Dockerfile.arm64 -t ${SERVER_IMAGE_NAME:-c-aibox-server}:${IMAGE_TAG:-latest} .
            case "$CLIENT_TYPE" in
                qt5)
                    log_info "Building Qt5 client GUI image..."
                    docker build -f docker/Dockerfile.client-gui -t ${CLIENT_IMAGE_NAME:-c-aibox-client}:${IMAGE_TAG:-latest} .
                    ;;
                web)
                    log_info "Building web client image..."
                    docker build -f docker/Dockerfile.web-client -t ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest} .
                    ;;
                both)
                    log_info "Building Qt5 client GUI image..."
                    docker build -f docker/Dockerfile.client-gui -t ${CLIENT_IMAGE_NAME:-c-aibox-client}:${IMAGE_TAG:-latest} .
                    log_info "Building web client image..."
                    docker build -f docker/Dockerfile.web-client -t ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest} .
                    ;;
            esac
            ;;
        web-client)
            log_info "Building web client image..."
            docker build -f docker/Dockerfile.web-client -t ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest} .
            ;;
    esac
    
    log_success "Images built successfully"
}

# Transfer files to Orange Pi
transfer_files() {
    log_step "Transferring Files to Orange Pi"
    local ssh_cmd=$(build_ssh_cmd)
    local scp_cmd=$(build_scp_cmd)
    
    # Create deployment directory
    $ssh_cmd "mkdir -p /home/<USER>/c-aibox-deploy"
    
    # Transfer docker-compose.yml
    log_info "Transferring docker-compose.yml..."
    $scp_cmd "$PROJECT_ROOT/docker-compose.yml" "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-deploy/"
    
    # Transfer .env file
    log_info "Transferring .env configuration..."
    $scp_cmd "$PROJECT_ROOT/.env" "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-deploy/"
    
    # Create data directories
    $ssh_cmd "mkdir -p /home/<USER>/c-aibox-deploy/{data,logs,models}"
    
    log_success "Files transferred successfully"
}

# Transfer Docker images
transfer_images() {
    log_step "Transferring Docker Images"
    local ssh_cmd=$(build_ssh_cmd)
    local scp_cmd=$(build_scp_cmd)
    
    cd "$PROJECT_ROOT"
    
    case "$COMPOSE_PROFILE" in
        combined)
            log_info "Saving and transferring combined image..."
            docker save ${COMBINED_IMAGE_NAME:-c-aibox-combined}:${IMAGE_TAG:-latest} | gzip > combined-image.tar.gz
            $scp_cmd combined-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-deploy/"
            $ssh_cmd "cd /home/<USER>/c-aibox-deploy && gunzip -c combined-image.tar.gz | docker load"
            rm combined-image.tar.gz
            ;;
        separate)
            log_info "Saving and transferring server image..."
            docker save ${SERVER_IMAGE_NAME:-c-aibox-server}:${IMAGE_TAG:-latest} | gzip > server-image.tar.gz
            $scp_cmd server-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-deploy/"

            case "$CLIENT_TYPE" in
                qt5)
                    log_info "Saving and transferring Qt5 client image..."
                    docker save ${CLIENT_IMAGE_NAME:-c-aibox-client}:${IMAGE_TAG:-latest} | gzip > client-image.tar.gz
                    $scp_cmd client-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-deploy/"
                    $ssh_cmd "cd /home/<USER>/c-aibox-deploy && gunzip -c server-image.tar.gz | docker load && gunzip -c client-image.tar.gz | docker load"
                    rm client-image.tar.gz
                    ;;
                web)
                    log_info "Saving and transferring web client image..."
                    docker save ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest} | gzip > web-client-image.tar.gz
                    $scp_cmd web-client-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-deploy/"
                    $ssh_cmd "cd /home/<USER>/c-aibox-deploy && gunzip -c server-image.tar.gz | docker load && gunzip -c web-client-image.tar.gz | docker load"
                    rm web-client-image.tar.gz
                    ;;
                both)
                    log_info "Saving and transferring Qt5 client image..."
                    docker save ${CLIENT_IMAGE_NAME:-c-aibox-client}:${IMAGE_TAG:-latest} | gzip > client-image.tar.gz
                    $scp_cmd client-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-deploy/"

                    log_info "Saving and transferring web client image..."
                    docker save ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest} | gzip > web-client-image.tar.gz
                    $scp_cmd web-client-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-deploy/"

                    $ssh_cmd "cd /home/<USER>/c-aibox-deploy && gunzip -c server-image.tar.gz | docker load && gunzip -c client-image.tar.gz | docker load && gunzip -c web-client-image.tar.gz | docker load"
                    rm client-image.tar.gz web-client-image.tar.gz
                    ;;
            esac
            rm server-image.tar.gz
            ;;
        web-client)
            log_info "Saving and transferring web client image..."
            docker save ${CLIENT_WEB_IMAGE_NAME:-c-aibox-web-client}:${IMAGE_TAG:-latest} | gzip > web-client-image.tar.gz
            $scp_cmd web-client-image.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox-deploy/"
            $ssh_cmd "cd /home/<USER>/c-aibox-deploy && gunzip -c web-client-image.tar.gz | docker load"
            rm web-client-image.tar.gz
            ;;
    esac
    
    log_success "Images transferred successfully"
}

# Deploy with Docker Compose
deploy_services() {
    log_step "Deploying Services with Docker Compose"
    local ssh_cmd=$(build_ssh_cmd)
    
    # Stop existing services
    if [[ "$REMOVE_OLD" == "true" ]]; then
        log_info "Stopping existing services..."
        $ssh_cmd "cd /home/<USER>/c-aibox-deploy && docker compose --profile $COMPOSE_PROFILE down" || true
    fi
    
    if [[ "$START_SERVICES" == "true" ]]; then
        log_info "Starting services with profile: $COMPOSE_PROFILE"
        $ssh_cmd "cd /home/<USER>/c-aibox-deploy && docker compose --profile $COMPOSE_PROFILE up -d"
        
        # Wait for services to start
        sleep 10
        
        # Check service status
        $ssh_cmd "cd /home/<USER>/c-aibox-deploy && docker compose --profile $COMPOSE_PROFILE ps"
    fi
    
    log_success "Services deployed successfully"
}

# Show deployment summary
show_summary() {
    log_step "Deployment Summary"
    local ssh_cmd=$(build_ssh_cmd)
    
    echo "Target: $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "Profile: $COMPOSE_PROFILE"
    echo ""
    
    # Show running containers
    log_info "Running containers:"
    $ssh_cmd "cd /home/<USER>/c-aibox-deploy && docker compose --profile $COMPOSE_PROFILE ps" || true
    
    echo ""
    log_success "Docker Compose deployment completed successfully!"
    log_info "Next steps:"
    
    if [[ "$COMPOSE_PROFILE" == "combined" || "$COMPOSE_PROFILE" == "separate" ]]; then
        log_info "  1. Access API: http://$ORANGE_PI_IP:${SERVER_HOST_PORT:-8080}"
        if [[ "$CLIENT_TYPE" == "qt5" || "$CLIENT_TYPE" == "both" ]]; then
            log_info "  2. Access Qt5 GUI via VNC: $ORANGE_PI_IP:${CLIENT_VNC_PORT:-5900}"
            log_info "     VNC Password: ${VNC_PASSWORD:-c-aibox123}"
            log_info "  3. Web placeholder: http://$ORANGE_PI_IP:${CLIENT_WEB_NGINX_PORT:-8081}"
        fi
        if [[ "$CLIENT_TYPE" == "web" || "$CLIENT_TYPE" == "both" ]]; then
            log_info "  4. Web Client: http://$ORANGE_PI_IP:${CLIENT_WEB_PORT:-3000}"
        fi
    fi

    if [[ "$COMPOSE_PROFILE" == "web-client" ]]; then
        log_info "  1. Web Client: http://$ORANGE_PI_IP:${CLIENT_WEB_PORT:-3000}"
        log_info "  2. Web Client (Nginx): http://$ORANGE_PI_IP:${CLIENT_WEB_NGINX_PORT:-8081}"
    fi
    
    log_info "  3. View logs: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'cd c-aibox-deploy && docker compose logs'"
    log_info "  4. Stop services: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'cd c-aibox-deploy && docker compose down'"
}

# Main execution
main() {
    log_step "Docker Compose Deployment to Orange Pi"
    
    test_ssh_connection
    check_docker_on_device
    build_images
    transfer_files
    transfer_images
    deploy_services
    show_summary
}

# Run main function
main "$@"
