#!/bin/bash

# Full Stack Deployment Script for C-AIBOX
# Deploys both server and client GUI to Orange Pi with Docker support

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Load environment configuration
load_env_config() {
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        log_info "Loading environment configuration from .env"
        source "$PROJECT_ROOT/.env"
    else
        log_error ".env file not found. Please create one based on the template."
        exit 1
    fi
}

# Default configuration (can be overridden by .env or command line)
DEPLOYMENT_MODE="combined"  # combined, separate, server-only, client-only
DOCKER_ENABLED=true
GUI_ENABLED=true
VNC_ENABLED=true
INSTALL_DEPS=true
START_SERVICES=true
BACKUP_EXISTING=true

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --mode)
            DEPLOYMENT_MODE="$2"
            shift 2
            ;;
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --user)
            ORANGE_PI_USER="$2"
            shift 2
            ;;
        --no-docker)
            DOCKER_ENABLED=false
            shift
            ;;
        --no-gui)
            GUI_ENABLED=false
            shift
            ;;
        --no-vnc)
            VNC_ENABLED=false
            shift
            ;;
        --no-deps)
            INSTALL_DEPS=false
            shift
            ;;
        --no-start)
            START_SERVICES=false
            shift
            ;;
        --no-backup)
            BACKUP_EXISTING=false
            shift
            ;;
        --help)
            cat << EOF
Full Stack Deployment Script for C-AIBOX

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --mode MODE         Deployment mode: combined, separate, server-only, client-only (default: combined)
    --ip IP             Orange Pi IP address (can be set in .env as ORANGE_PI_IP)
    --user USER         SSH username (can be set in .env as ORANGE_PI_USER)
    --no-docker         Deploy without Docker (native binaries)
    --no-gui            Deploy server only (no GUI client)
    --no-vnc            Disable VNC server for remote GUI access
    --no-deps           Skip dependency installation
    --no-start          Don't start services after deployment
    --no-backup         Don't backup existing installation
    --help              Show this help

DEPLOYMENT MODES:
    combined            Single Docker container with server + client GUI
    separate            Separate Docker containers for server and client
    server-only         Deploy only the API server
    client-only         Deploy only the GUI client

EXAMPLES:
    $0                                          # Deploy combined stack using .env config
    $0 --mode separate --ip ***************    # Deploy separate containers
    $0 --mode server-only --no-docker          # Deploy only server as native binary
    $0 --mode client-only --no-vnc             # Deploy only client without VNC

PREREQUISITES:
    1. Orange Pi 5 Plus with SSH enabled
    2. Cross-compiled build in build-orangepi directory
    3. Configured .env file with deployment settings
    4. SSH access to Orange Pi (password or key-based)
EOF
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Load environment configuration
load_env_config

# Validate configuration
validate_config() {
    log_step "Validating Configuration"
    
    if [[ -z "$ORANGE_PI_IP" ]]; then
        log_error "Orange Pi IP address is required (set ORANGE_PI_IP in .env or use --ip)"
        exit 1
    fi
    
    if [[ "$DEPLOYMENT_MODE" != "combined" && "$DEPLOYMENT_MODE" != "separate" && 
          "$DEPLOYMENT_MODE" != "server-only" && "$DEPLOYMENT_MODE" != "client-only" ]]; then
        log_error "Invalid deployment mode: $DEPLOYMENT_MODE"
        exit 1
    fi
    
    log_success "Configuration validated"
    log_info "Deployment Mode: $DEPLOYMENT_MODE"
    log_info "Target: $ORANGE_PI_USER@$ORANGE_PI_IP"
    log_info "Docker Enabled: $DOCKER_ENABLED"
    log_info "GUI Enabled: $GUI_ENABLED"
}

# SSH command builder
build_ssh_cmd() {
    local ssh_cmd="ssh"
    if [[ -n "$ORANGE_PI_SSH_KEY" ]]; then
        ssh_cmd="$ssh_cmd -i $ORANGE_PI_SSH_KEY"
    fi
    ssh_cmd="$ssh_cmd -p ${ORANGE_PI_PORT:-22} $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "$ssh_cmd"
}

# Test SSH connection
test_ssh_connection() {
    log_step "Testing SSH Connection"
    local ssh_cmd=$(build_ssh_cmd)
    
    if $ssh_cmd "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "SSH connection failed to $ORANGE_PI_IP"
        exit 1
    fi
}

# Install dependencies on Orange Pi
install_dependencies() {
    if [[ "$INSTALL_DEPS" != "true" ]]; then
        return
    fi
    
    log_step "Installing Dependencies on Orange Pi"
    local ssh_cmd=$(build_ssh_cmd)
    
    # Install Docker if needed
    if [[ "$DOCKER_ENABLED" == "true" ]]; then
        log_info "Installing Docker..."
        $ssh_cmd "curl -fsSL https://get.docker.com | sh && sudo usermod -aG docker $ORANGE_PI_USER" || {
            log_warning "Docker installation failed"
        }
    fi
    
    # Install GUI dependencies if needed
    if [[ "$GUI_ENABLED" == "true" && "$DOCKER_ENABLED" == "false" ]]; then
        log_info "Installing GUI dependencies..."
        $ssh_cmd "sudo apt-get update && sudo apt-get install -y xorg lightdm qt5-default" || {
            log_warning "GUI dependencies installation failed"
        }
    fi
    
    log_success "Dependencies installation completed"
}

# Deploy based on mode
deploy_services() {
    log_step "Deploying Services"
    
    case "$DEPLOYMENT_MODE" in
        combined)
            deploy_combined
            ;;
        separate)
            deploy_separate
            ;;
        server-only)
            deploy_server_only
            ;;
        client-only)
            deploy_client_only
            ;;
    esac
}

# Deploy combined stack
deploy_combined() {
    log_info "Deploying combined server + client stack..."
    
    if [[ "$DOCKER_ENABLED" == "true" ]]; then
        # Use Docker Compose for combined deployment
        "$SCRIPT_DIR/deploy-docker-compose.sh" --profile combined --ip "$ORANGE_PI_IP"
    else
        # Deploy native binaries
        "$SCRIPT_DIR/deploy-to-orangepi.sh" --ip "$ORANGE_PI_IP"
    fi
}

# Deploy separate containers
deploy_separate() {
    log_info "Deploying separate server and client containers..."
    
    if [[ "$DOCKER_ENABLED" == "true" ]]; then
        "$SCRIPT_DIR/deploy-docker-compose.sh" --profile separate --ip "$ORANGE_PI_IP"
    else
        log_error "Separate mode requires Docker. Use --mode combined with --no-docker for native deployment."
        exit 1
    fi
}

# Deploy server only
deploy_server_only() {
    log_info "Deploying server only..."
    "$SCRIPT_DIR/deploy-docker-simple.sh" --ip "$ORANGE_PI_IP"
}

# Deploy client only
deploy_client_only() {
    log_info "Deploying client GUI only..."
    "$SCRIPT_DIR/deploy-client-gui.sh" --ip "$ORANGE_PI_IP"
}

# Show deployment summary
show_summary() {
    log_step "Deployment Summary"
    
    echo "Target: $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "Mode: $DEPLOYMENT_MODE"
    echo "Docker: $DOCKER_ENABLED"
    echo "GUI: $GUI_ENABLED"
    echo ""
    
    log_success "Full stack deployment completed successfully!"
    log_info "Next steps:"
    
    if [[ "$DEPLOYMENT_MODE" == "combined" || "$DEPLOYMENT_MODE" == "server-only" ]]; then
        log_info "  1. Access API: http://$ORANGE_PI_IP:${SERVER_HOST_PORT:-8080}"
    fi
    
    if [[ "$DEPLOYMENT_MODE" == "combined" || "$DEPLOYMENT_MODE" == "separate" || "$DEPLOYMENT_MODE" == "client-only" ]] && [[ "$GUI_ENABLED" == "true" ]]; then
        if [[ "$VNC_ENABLED" == "true" ]]; then
            log_info "  2. Access GUI via VNC: $ORANGE_PI_IP:${CLIENT_VNC_PORT:-5900}"
            log_info "     VNC Password: ${VNC_PASSWORD:-c-aibox123}"
        fi
        log_info "  3. SSH to Orange Pi: ssh $ORANGE_PI_USER@$ORANGE_PI_IP"
    fi
}

# Main execution
main() {
    log_step "C-AIBOX Full Stack Deployment"
    
    validate_config
    test_ssh_connection
    install_dependencies
    deploy_services
    show_summary
}

# Make script executable
chmod +x "$SCRIPT_DIR/deploy-full-stack.sh"

# Run main function
main "$@"
