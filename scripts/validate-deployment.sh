#!/bin/bash

# Deployment Validation Script for C-AIBOX
# Run this before deploying to Orange Pi to catch issues early

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Validation counters
CHECKS_PASSED=0
CHECKS_FAILED=0
CHECKS_WARNING=0

# Check function
check() {
    local description="$1"
    local command="$2"
    local is_warning="${3:-false}"

    printf "%-50s" "Checking $description..."

    if timeout 10 bash -c "$command" >/dev/null 2>&1; then
        echo -e " ${GREEN}[✓]${NC}"
        ((CHECKS_PASSED++))
        return 0
    else
        if [[ "$is_warning" == "true" ]]; then
            echo -e " ${YELLOW}[⚠]${NC} (optional)"
            ((CHECKS_WARNING++))
            return 1
        else
            echo -e " ${RED}[✗]${NC}"
            ((CHECKS_FAILED++))
            return 1
        fi
    fi
}

# Main validation
main() {
    log_step "C-AIBOX Deployment Validation"
    echo "This script validates your deployment setup before deploying to Orange Pi"
    echo ""
    
    cd "$PROJECT_ROOT"
    
    # 1. Check required tools
    log_step "1. Required Tools"
    check "Docker installed" "docker --version"
    check "Docker Compose installed" "docker compose version"
    check "SSH client available" "ssh -V"
    check "SCP available" "scp -h" true
    check "Curl available" "curl --version" true
    
    # 2. Check project structure
    log_step "2. Project Structure"
    check "Project root exists" "test -d ."
    check "Apps directory exists" "test -d apps"
    check "Docker directory exists" "test -d docker"
    check "Scripts directory exists" "test -d scripts"
    check "Build container directory exists" "test -d build-container"
    
    # 3. Check binaries
    log_step "3. Built Binaries"
    check "Server binary exists" "test -f build-container/bin/server"
    check "Client binary exists" "test -f build-container/bin/client_app"
    check "Server binary is executable" "test -x build-container/bin/server"
    check "Client binary is executable" "test -x build-container/bin/client_app"
    
    # 4. Check configuration files
    log_step "4. Configuration Files"
    check ".env file exists" "test -f .env"
    check "docker-compose.yml exists" "test -f docker-compose.yml"
    check ".env.template exists" "test -f .env.template"
    
    # 5. Check Dockerfiles
    log_step "5. Dockerfiles"
    check "Combined Dockerfile exists" "test -f docker/Dockerfile.combined"
    check "Client GUI Dockerfile exists" "test -f docker/Dockerfile.client-gui"
    check "Web client Dockerfile exists" "test -f docker/Dockerfile.web-client"
    check "ARM64 Dockerfile exists" "test -f docker/Dockerfile.arm64"
    
    # 6. Check deployment scripts
    log_step "6. Deployment Scripts"
    check "Docker compose deployment script exists" "test -f scripts/deploy/deploy-docker-compose.sh"
    check "Client GUI deployment script exists" "test -f scripts/deploy/deploy-client-gui.sh"
    check "Quick demo script exists" "test -f scripts/deploy/quick-deploy-demo.sh"
    check "Docker compose script is executable" "test -x scripts/deploy/deploy-docker-compose.sh"
    check "Client GUI script is executable" "test -x scripts/deploy/deploy-client-gui.sh"
    check "Quick demo script is executable" "test -x scripts/deploy/quick-deploy-demo.sh"
    
    # 7. Check script syntax
    log_step "7. Script Syntax Validation"
    check "Docker compose script syntax" "bash -n scripts/deploy/deploy-docker-compose.sh"
    check "Client GUI script syntax" "bash -n scripts/deploy/deploy-client-gui.sh"
    check "Quick demo script syntax" "bash -n scripts/deploy/quick-deploy-demo.sh"
    
    # 8. Check Docker Compose configuration
    log_step "8. Docker Compose Configuration"
    check "Docker compose config is valid" "docker compose config"
    check "Combined profile is valid" "docker compose --profile combined config"
    check "Separate profile is valid" "docker compose --profile separate config"
    check "Web client profile is valid" "docker compose --profile web-client config"
    
    # 9. Check environment configuration
    log_step "9. Environment Configuration"
    if [[ -f .env ]]; then
        check "ORANGE_PI_IP is set" "grep -q '^ORANGE_PI_IP=' .env"
        check "ORANGE_PI_USER is set" "grep -q '^ORANGE_PI_USER=' .env"
        check "CLIENT_TYPE is set" "grep -q '^CLIENT_TYPE=' .env"
        check "VNC_PASSWORD is set" "grep -q '^VNC_PASSWORD=' .env"
    else
        log_warning "No .env file found - copy from .env.template"
        ((CHECKS_WARNING++))
    fi
    
    # 10. Check documentation
    log_step "10. Documentation"
    check "README.md exists" "test -f README.md"
    check "Client deployment guide exists" "test -f docs/CLIENT_GUI_DEPLOYMENT_GUIDE.md" true
    
    # Summary
    echo ""
    log_step "Validation Summary"
    echo "✓ Checks passed: $CHECKS_PASSED"
    if [[ $CHECKS_WARNING -gt 0 ]]; then
        echo "⚠ Warnings: $CHECKS_WARNING"
    fi
    if [[ $CHECKS_FAILED -gt 0 ]]; then
        echo "✗ Checks failed: $CHECKS_FAILED"
    fi
    
    echo ""
    if [[ $CHECKS_FAILED -eq 0 ]]; then
        log_success "All critical checks passed! Ready for deployment."
        echo ""
        echo "Next steps:"
        echo "1. Configure your .env file with Orange Pi details"
        echo "2. Test SSH connection to your Orange Pi"
        echo "3. Run deployment:"
        echo "   ./scripts/deploy/quick-deploy-demo.sh --ip YOUR_ORANGE_PI_IP"
        echo ""
        exit 0
    else
        log_error "Some critical checks failed. Please fix the issues before deploying."
        echo ""
        echo "Common fixes:"
        echo "1. Build the project: cmake --build build-container"
        echo "2. Copy .env.template to .env and configure it"
        echo "3. Make scripts executable: chmod +x scripts/deploy/*.sh"
        echo ""
        exit 1
    fi
}

# Show help
show_help() {
    cat << EOF
C-AIBOX Deployment Validation Script

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --help              Show this help

DESCRIPTION:
    This script validates your C-AIBOX deployment setup before deploying
    to Orange Pi. It checks:
    
    - Required tools (Docker, SSH, etc.)
    - Project structure and files
    - Built binaries
    - Configuration files
    - Dockerfiles and scripts
    - Docker Compose configuration
    - Environment settings
    
    Run this script to catch issues early before attempting deployment
    to your Orange Pi device.

EXAMPLES:
    $0                  # Run full validation
    $0 --help           # Show this help
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
