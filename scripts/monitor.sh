#!/bin/bash

# C-AIBOX Monitoring Script
# This script monitors the deployed C-AIBOX services on the Orange Pi

set -e

# Configuration
REMOTE_HOST="${REMOTE_HOST:-***************}"
REMOTE_USER="${REMOTE_USER:-orangepi}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check connectivity
check_connectivity() {
    log_info "Checking connectivity to $REMOTE_USER@$REMOTE_HOST..."
    if ssh -o ConnectTimeout=5 -o BatchMode=yes $REMOTE_USER@$REMOTE_HOST exit 2>/dev/null; then
        log_success "Connected to remote host"
        return 0
    else
        log_error "Cannot connect to $REMOTE_USER@$REMOTE_HOST"
        return 1
    fi
}

# Check Docker status
check_docker() {
    log_info "Checking Docker status..."
    if ssh $REMOTE_USER@$REMOTE_HOST "docker info >/dev/null 2>&1"; then
        log_success "Docker is running"
        return 0
    else
        log_error "Docker is not running or not accessible"
        return 1
    fi
}

# Check running containers
check_containers() {
    log_info "Checking running containers..."
    
    local containers=$(ssh $REMOTE_USER@$REMOTE_HOST "docker ps --format '{{.Names}}' | grep c-aibox" 2>/dev/null || echo "")
    
    if [ -z "$containers" ]; then
        log_warning "No C-AIBOX containers are running"
        return 1
    else
        log_success "Found running containers:"
        echo "$containers" | while read container; do
            echo "  - $container"
        done
        return 0
    fi
}

# Check container health
check_container_health() {
    log_info "Checking container health..."
    
    # Check server container
    if ssh $REMOTE_USER@$REMOTE_HOST "docker ps | grep c-aibox-simple-server" >/dev/null 2>&1; then
        log_success "Server container is running"
        
        # Check server health endpoint
        if ssh $REMOTE_USER@$REMOTE_HOST "curl -s http://localhost:8080/health" >/dev/null 2>&1; then
            log_success "Server health check passed"
        else
            log_warning "Server health check failed"
        fi
    else
        log_warning "Server container is not running"
    fi
    
    # Check web client container
    if ssh $REMOTE_USER@$REMOTE_HOST "docker ps | grep c-aibox-simple-web-client" >/dev/null 2>&1; then
        log_success "Web client container is running"
        
        # Check web client accessibility
        if ssh $REMOTE_USER@$REMOTE_HOST "curl -s http://localhost:3000/" >/dev/null 2>&1; then
            log_success "Web client is accessible"
        else
            log_warning "Web client is not accessible"
        fi
    else
        log_warning "Web client container is not running"
    fi
}

# Check resource usage
check_resources() {
    log_info "Checking system resources..."
    
    # CPU usage
    local cpu_usage=$(ssh $REMOTE_USER@$REMOTE_HOST "top -bn1 | grep 'Cpu(s)' | awk '{print \$2}' | cut -d'%' -f1")
    log_info "CPU Usage: ${cpu_usage}%"
    
    # Memory usage
    local memory_info=$(ssh $REMOTE_USER@$REMOTE_HOST "free -h | grep Mem")
    log_info "Memory: $memory_info"
    
    # Disk usage
    local disk_usage=$(ssh $REMOTE_USER@$REMOTE_HOST "df -h / | tail -1 | awk '{print \$5}'")
    log_info "Disk Usage: $disk_usage"
    
    # Docker stats
    log_info "Container resource usage:"
    ssh $REMOTE_USER@$REMOTE_HOST "docker stats --no-stream --format 'table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}' | grep c-aibox" 2>/dev/null || log_warning "No C-AIBOX containers found for stats"
}

# Check logs
check_logs() {
    log_info "Checking recent container logs..."
    
    # Server logs
    if ssh $REMOTE_USER@$REMOTE_HOST "docker ps | grep c-aibox-simple-server" >/dev/null 2>&1; then
        log_info "Server container logs (last 10 lines):"
        ssh $REMOTE_USER@$REMOTE_HOST "docker logs --tail 10 c-aibox-simple-server 2>/dev/null" || log_warning "Could not retrieve server logs"
    fi
    
    # Web client logs
    if ssh $REMOTE_USER@$REMOTE_HOST "docker ps | grep c-aibox-simple-web-client" >/dev/null 2>&1; then
        log_info "Web client container logs (last 10 lines):"
        ssh $REMOTE_USER@$REMOTE_HOST "docker logs --tail 10 c-aibox-simple-web-client 2>/dev/null" || log_warning "Could not retrieve web client logs"
    fi
}

# Test API endpoints
test_api() {
    log_info "Testing API endpoints..."
    
    # Test health endpoint
    local health_response=$(ssh $REMOTE_USER@$REMOTE_HOST "curl -s http://localhost:8080/health" 2>/dev/null || echo "")
    if [ -n "$health_response" ]; then
        log_success "Health endpoint: $health_response"
    else
        log_error "Health endpoint not responding"
    fi
    
    # Test status endpoint
    local status_response=$(ssh $REMOTE_USER@$REMOTE_HOST "curl -s http://localhost:8080/api/status" 2>/dev/null || echo "")
    if [ -n "$status_response" ]; then
        log_success "Status endpoint: $status_response"
    else
        log_error "Status endpoint not responding"
    fi
    
    # Test models endpoint
    local models_response=$(ssh $REMOTE_USER@$REMOTE_HOST "curl -s http://localhost:8080/api/models" 2>/dev/null || echo "")
    if [ -n "$models_response" ]; then
        log_success "Models endpoint: $models_response"
    else
        log_error "Models endpoint not responding"
    fi
}

# Show access URLs
show_access_info() {
    log_info "Access URLs:"
    echo "  Server API: http://$REMOTE_HOST:8080"
    echo "  Web Client: http://$REMOTE_HOST:3000"
    echo "  Web Client (Alt): http://$REMOTE_HOST:8081"
    echo ""
    log_info "Quick test commands:"
    echo "  curl http://$REMOTE_HOST:8080/health"
    echo "  curl http://$REMOTE_HOST:8080/api/status"
    echo "  curl http://$REMOTE_HOST:8080/api/models"
}

# Main monitoring function
main() {
    echo "======================================"
    echo "C-AIBOX Service Monitor"
    echo "======================================"
    echo ""
    
    if ! check_connectivity; then
        exit 1
    fi
    
    if ! check_docker; then
        exit 1
    fi
    
    check_containers
    check_container_health
    check_resources
    
    if [ "$1" = "--logs" ]; then
        check_logs
    fi
    
    if [ "$1" = "--test" ]; then
        test_api
    fi
    
    echo ""
    show_access_info
    
    log_success "Monitoring completed!"
}

# Parse command line arguments
case $1 in
    --help)
        echo "Usage: $0 [OPTIONS]"
        echo "Options:"
        echo "  --logs       Show recent container logs"
        echo "  --test       Test API endpoints"
        echo "  --help       Show this help message"
        echo ""
        echo "Environment variables:"
        echo "  REMOTE_HOST  Remote host (default: ***************)"
        echo "  REMOTE_USER  Remote user (default: orangepi)"
        exit 0
        ;;
    --logs|--test)
        main $1
        ;;
    *)
        main
        ;;
esac
